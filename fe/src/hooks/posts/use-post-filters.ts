/**
 * Advanced Post Filter System with Event-Driven Architecture
 *
 * Performance Features:
 * - Zero re-renders in posts list when filters change
 * - Individual chip state management
 * - Event-driven communication
 * - Minimal React reconciliation
 * - World-class performance optimization
 */

import { useCallback, useRef, useEffect, useMemo, useState } from 'react'
import type { PostFilterType } from '@/components/posts/post-filter-chips'

// ============================================================================
// EVENT SYSTEM - Decoupled Communication
// ============================================================================

type FilterChangeEvent = CustomEvent<{ filter: PostFilterType }>

class PostFilterEventBus {
  private static instance: PostFilterEventBus
  private eventTarget = new EventTarget()
  private currentFilter: PostFilterType = 'all'

  static getInstance(): PostFilterEventBus {
    if (!PostFilterEventBus.instance) {
      PostFilterEventBus.instance = new PostFilterEventBus()
    }
    return PostFilterEventBus.instance
  }

  // Get current filter without subscribing to changes
  getCurrentFilter(): PostFilterType {
    return this.currentFilter
  }

  // Set filter and emit event (no React re-renders)
  setFilter(filter: PostFilterType): void {
    if (this.currentFilter !== filter) {
      this.currentFilter = filter
      this.eventTarget.dispatchEvent(
        new CustomEvent('filterChange', { detail: { filter } })
      )
    }
  }

  // Subscribe to filter changes
  subscribe(callback: (filter: PostFilterType) => void): () => void {
    const handler = (event: Event) => {
      const filterEvent = event as FilterChangeEvent
      callback(filterEvent.detail.filter)
    }

    this.eventTarget.addEventListener('filterChange', handler)

    // Return unsubscribe function
    return () => {
      this.eventTarget.removeEventListener('filterChange', handler)
    }
  }
}

// Singleton instance
const filterEventBus = PostFilterEventBus.getInstance()

// ============================================================================
// HOOKS - Performance Optimized
// ============================================================================

/**
 * Hook for individual filter chips - manages own state, minimal re-renders
 */
export function useFilterChip(chipFilter: PostFilterType) {
  const isSelectedRef = useRef(filterEventBus.getCurrentFilter() === chipFilter)
  const forceUpdateRef = useRef<() => void>()

  // Force update function for this specific chip only
  const forceUpdate = useCallback(() => {
    forceUpdateRef.current?.()
  }, [])

  // Subscribe to filter changes and update only if this chip is affected
  useEffect(() => {
    const unsubscribe = filterEventBus.subscribe((newFilter) => {
      const wasSelected = isSelectedRef.current
      const isNowSelected = newFilter === chipFilter

      // Only force update if selection state changed for this chip
      if (wasSelected !== isNowSelected) {
        isSelectedRef.current = isNowSelected
        forceUpdate()
      }
    })

    return unsubscribe
  }, [chipFilter, forceUpdate])

  // Set up force update mechanism
  const [, setTick] = useState(0)
  forceUpdateRef.current = useCallback(() => {
    setTick(tick => tick + 1)
  }, [])

  const handleClick = useCallback(() => {
    filterEventBus.setFilter(chipFilter)
  }, [chipFilter])

  return {
    isSelected: isSelectedRef.current,
    onClick: handleClick
  }
}

/**
 * Hook for posts list - subscribes to filter changes without re-rendering parent
 */
export function usePostsFilter() {
  const currentFilterRef = useRef(filterEventBus.getCurrentFilter())
  const callbackRef = useRef<(filter: PostFilterType) => void>()

  // Subscribe to filter changes
  useEffect(() => {
    const unsubscribe = filterEventBus.subscribe((newFilter) => {
      currentFilterRef.current = newFilter
      callbackRef.current?.(newFilter)
    })

    return unsubscribe
  }, [])

  // Register callback for filter changes (doesn't cause re-renders)
  const onFilterChange = useCallback((callback: (filter: PostFilterType) => void) => {
    callbackRef.current = callback
    // Call immediately with current filter
    callback(currentFilterRef.current)
  }, [])

  return {
    currentFilter: currentFilterRef.current,
    onFilterChange
  }
}

/**
 * Hook for getting current filter state (read-only, no subscriptions)
 */
export function useCurrentFilter(): PostFilterType {
  return useMemo(() => filterEventBus.getCurrentFilter(), [])
}

// ============================================================================
// UTILITIES
// ============================================================================

// Type guards for validation
export function isValidPostFilter(value: string | null): value is PostFilterType {
  return value !== null && [
    'all',
    'assigned_to_me',
    'needs_review',
    'my_pending',
    'my_approved',
    'my_rework',
    'reviewed_by_me'
  ].includes(value)
}

// Initialize filter from URL on app start
export function initializeFilterFromUrl(): void {
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search)
    const urlFilter = urlParams.get('filter') as PostFilterType

    if (isValidPostFilter(urlFilter)) {
      filterEventBus.setFilter(urlFilter)
    }
  }
}
