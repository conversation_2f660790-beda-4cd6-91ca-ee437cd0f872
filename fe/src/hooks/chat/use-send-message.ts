import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for sending chat messages.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the messages query on success to show the new message.
 * Also updates the channel list to reflect the latest message.
 * 
 * The backend automatically:
 * - Validates user permissions to write to the channel
 * - Parses mentions from message content
 * - <PERSON><PERSON> file attachments
 * - Broadcasts the message via WebSocket to other participants
 * - Sends notifications for mentions
 * 
 * @param channelId - Channel ID to send message to
 */
export function useSendMessage(channelId: number) {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/chats/{channelId}/messages', {
    onSuccess: () => {
      // Invalidate and refetch messages for this channel
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/chats/{channelId}/messages', { channelId }],
      });

      // Invalidate channel list to update last message and unread counts
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats'],
      });

      // Optionally, we could optimistically update the messages cache here
      // but since we have WebSocket updates, we'll rely on those for real-time updates
    },
    onError: (error) => {
      console.error('Failed to send message:', error);
    },
  });
}

/**
 * Custom hook for updating/editing chat messages.
 * 
 * @param channelId - Channel ID
 * @param messageId - Message ID to update
 */
export function useUpdateMessage(channelId: number) {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/chats/{channelId}/messages/{messageId}', {
    onSuccess: () => {
      // Invalidate messages to show the updated message
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/chats/{channelId}/messages', { channelId }],
      });
    },
    onError: (error) => {
      console.error('Failed to update message:', error);
    },
  });
}

/**
 * Custom hook for deleting chat messages.
 * 
 * @param channelId - Channel ID
 * @param messageId - Message ID to delete
 */
export function useDeleteMessage(channelId: number) {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/chats/{channelId}/messages/{messageId}', {
    onSuccess: () => {
      // Invalidate messages to remove the deleted message
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/chats/{channelId}/messages', { channelId }],
      });

      // Invalidate channel list in case this was the last message
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats'],
      });
    },
    onError: (error) => {
      console.error('Failed to delete message:', error);
    },
  });
}
