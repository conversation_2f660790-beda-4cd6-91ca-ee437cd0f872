import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from '@/lib/i18n/typed-translations';

/**
 * Custom hook for fetching chat channels for a collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * The backend automatically:
 * - Scopes channels to the current account (multi-tenancy)
 * - Filters channels based on user permissions
 * - Returns channels with participant counts and last messages
 * - Includes unread message counts
 *
 * @param hubId - Hub ID to fetch channels for
 * @param options - Query options including enabled and staleTime
 */
export function useChatChannels(
  hubId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/chats', {
    params: {
      path: { hubId },
      query: {
        page: 0,
        size: 50, // Most hubs won't have more than 50 channels
      },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId,
    // Cache data for 30 seconds (channels change less frequently)
    staleTime: options?.staleTime ?? 30 * 1000,
    // Enable automatic refetching on window focus for fresh unread counts
    refetchOnWindowFocus: true,
  });
}

/**
 * Custom hook for fetching a specific chat channel details.
 *
 * @param hubId - Hub ID
 * @param channelId - Channel ID to fetch details for
 * @param options - Query options including enabled and staleTime
 */
export function useChatChannel(
  hubId: number,
  channelId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/chats/{channelId}', {
    params: {
      path: { hubId, channelId },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId && !!channelId,
    // Cache data for 1 minute
    staleTime: options?.staleTime ?? 1 * 60 * 1000,
  });
}

/**
 * Custom hook for creating a custom chat channel.
 *
 * @param hubId - Hub ID to create channel in
 */
export function useCreateChannel(hubId: number) {
  const queryClient = useQueryClient();
  const { t, keys } = useTranslations();

  return $api.useMutation('post', '/api/hubs/{hubId}/chats', {
    onSuccess: () => {
      // Invalidate channels list to show the new channel
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats', { params: { path: { hubId } } }],
      });

      // Show success toast notification
      toast.success(t(keys.collaborationHubs.chat.channelCreated));
    },
    onError: (error) => {
      console.error('Failed to create channel:', error);
    },
  });
}

/**
 * Custom hook for deleting a custom chat channel.
 *
 * @param hubId - Hub ID
 * @param channelId - Channel ID to delete
 */
export function useDeleteChannel(hubId: number) {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/hubs/{hubId}/chats/{channelId}', {
    onSuccess: () => {
      // Invalidate channels list to remove the deleted channel
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats', { hubId }],
      });
    },
    onError: (error) => {
      console.error('Failed to delete channel:', error);
    },
  });
}

/**
 * Custom hook for adding participants to a custom chat channel.
 *
 * @param hubId - Hub ID
 * @param channelId - Channel ID to add participants to
 */
export function useAddChannelParticipants(hubId: number, channelId: number) {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/hubs/{hubId}/chats/{channelId}/participants', {
    onSuccess: () => {
      // Invalidate channel details to update participant count
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats/{channelId}', { hubId, channelId }],
      });

      // Invalidate channels list to update participant counts
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats', { hubId }],
      });
    },
    onError: (error) => {
      console.error('Failed to add participants to channel:', error);
    },
  });
}

/**
 * Custom hook for removing participants from a custom chat channel.
 *
 * @param hubId - Hub ID
 * @param channelId - Channel ID to remove participants from
 */
export function useRemoveChannelParticipants(hubId: number, channelId: number) {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/hubs/{hubId}/chats/{channelId}/participants', {
    onSuccess: () => {
      // Invalidate channel details to update participant count
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats/{channelId}', { hubId, channelId }],
      });

      // Invalidate channels list to update participant counts
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats', { hubId }],
      });
    },
    onError: (error) => {
      console.error('Failed to remove participants from channel:', error);
    },
  });
}
