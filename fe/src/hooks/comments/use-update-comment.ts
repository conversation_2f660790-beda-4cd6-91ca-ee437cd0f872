import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for updating an existing comment.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the comments list query on success
 * to ensure the UI shows the updated comment.
 * 
 * The backend automatically:
 * - Validates user permissions (only comment author and admins can edit)
 * - Scopes to the current account (multi-tenancy)
 * - Updates the comment's updated_at timestamp
 */
export function useUpdateComment() {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/comments/{commentId}', {
    onSuccess: (data, variables) => {
      const commentId = variables.params.path.commentId;
      
      // Invalidate specific comment query
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/comments/{commentId}', { params: { path: { commentId } } }],
      });
      
      // Invalidate comments list for the post (we need to get postId from the response)
      if (data?.post_id) {
        queryClient.invalidateQueries({
          queryKey: ['get', '/api/posts/{postId}/comments', { params: { path: { postId: data.post_id } } }],
        });
      }
      
      // Invalidate all comments queries to be safe
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/posts/{postId}/comments'],
      });
    },
  });
}
