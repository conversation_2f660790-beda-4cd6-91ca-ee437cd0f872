import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for deleting a bank detail (soft delete).
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the bank details list query on success
 * to ensure the UI removes the deleted bank detail.
 */
export function useDeleteBankDetail() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/bank-details/{id}', {
    onSuccess: () => {
      // Invalidate and refetch bank details list
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/bank-details'],
      });
    },
  });
}
