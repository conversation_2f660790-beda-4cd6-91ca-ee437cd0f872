import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for creating a new bank detail.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the bank details list query on success
 * to ensure the UI shows the newly created bank detail.
 */
export function useCreateBankDetail() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/bank-details', {
    onSuccess: () => {
      // Invalidate and refetch bank details list
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/bank-details'],
      });
    },
  });
}
