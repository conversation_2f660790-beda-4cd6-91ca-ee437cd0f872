import { useCallback, useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Hook for managing media URL refresh to handle presigned URL expiration.
 * Implements error recovery for expired URLs.
 */
export function useMediaUrlRefresh() {
  const queryClient = useQueryClient();
  const refreshTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

  /**
   * Schedules proactive refresh of post data before URLs expire.
   * Refreshes at 70% of the URL expiration time for safety margin.
   */
  const scheduleUrlRefresh = useCallback((
    hubId: number,
    postId?: number,
    urlExpirationMinutes: number = 10 // Default for images
  ) => {
    const refreshKey = postId ? `post-${postId}` : `hub-${hubId}`;

    // Clear existing timeout if any
    const existingTimeout = refreshTimeouts.current.get(refreshKey);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Schedule refresh at 70% of expiration time
    const refreshDelayMs = urlExpirationMinutes * 60 * 1000 * 0.7;

    const timeout = setTimeout(() => {
      if (postId) {
        // Refresh specific post
        queryClient.invalidateQueries({
          queryKey: ['get', '/api/posts/{postId}', { postId }]
        });
      } else {
        // Refresh all posts in hub
        queryClient.invalidateQueries({
          queryKey: ['get', '/api/hubs/{hubId}/posts', { hubId }]
        });
      }

      refreshTimeouts.current.delete(refreshKey);
    }, refreshDelayMs);

    refreshTimeouts.current.set(refreshKey, timeout);
  }, [queryClient]);

  /**
   * Handles media load errors, particularly 403 errors from expired URLs.
   * Implements immediate refresh strategy for expired URLs.
   */
  const handleMediaError = useCallback(async (
    error: Event,
    hubId: number,
    postId?: number
  ) => {
    const target = error.target as HTMLImageElement | HTMLVideoElement;
    const url = target?.src;
    
    if (!url) return;

    // Check if this is likely a 403 error (expired URL)
    // We can't directly access the HTTP status from img/video onerror,
    // but we can detect patterns in the URL or implement a fetch check
    const isPresignedUrl = url.includes('X-Amz-Signature') || url.includes('Expires=');
    
    if (isPresignedUrl) {
      console.warn('Media URL appears to be expired, refreshing post data:', url);
      
      if (postId) {
        // Refresh specific post immediately
        await queryClient.invalidateQueries({
          queryKey: ['get', '/api/posts/{postId}', { postId }]
        });
      } else {
        // Refresh all posts in hub
        await queryClient.invalidateQueries({
          queryKey: ['get', '/api/hubs/{hubId}/posts', { hubId }]
        });
      }
    }
  }, [queryClient]);

  /**
   * Verifies if a URL is accessible and refreshes data if not.
   * Useful for proactive checking of URL validity.
   */
  const verifyAndRefreshUrl = useCallback(async (
    url: string,
    hubId: number,
    postId?: number
  ): Promise<boolean> => {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      
      if (response.status === 403) {
        console.warn('URL verification failed with 403, refreshing data:', url);
        
        if (postId) {
          await queryClient.invalidateQueries({
            queryKey: ['get', '/api/posts/{postId}', { postId }]
          });
        } else {
          await queryClient.invalidateQueries({
            queryKey: ['get', '/api/hubs/{hubId}/posts', { hubId }]
          });
        }
        
        return false;
      }
      
      return response.ok;
    } catch (error) {
      console.warn('URL verification failed:', error);
      return false;
    }
  }, [queryClient]);

  // Cleanup on unmount
  useEffect(() => {
    const timeouts = refreshTimeouts.current;
    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
      timeouts.clear();
    };
  }, []);

  return {
    scheduleUrlRefresh,
    handleMediaError,
    verifyAndRefreshUrl
  };
}


