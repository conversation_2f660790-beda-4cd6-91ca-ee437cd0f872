import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for sending invoice email.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * Automatically invalidates related queries on success to ensure
 * the UI shows updated delivery status.
 *
 * @param force - Optional parameter to force send even if already sent
 */
export function useSendInvoice() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/invoices/{id}/send', {
    onSuccess: (_data, variables) => {
      // Invalidate and refetch invoices list
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/invoices'],
      });

      // Invalidate specific invoice query
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/invoices/{id}', { params: { path: { id: variables.params.path.id } } }],
      });
    },
  });
}
