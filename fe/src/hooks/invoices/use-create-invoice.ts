import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for creating a new invoice.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the invoices list query on success
 * to ensure the UI shows the newly created invoice.
 */
export function useCreateInvoice() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/invoices', {
    onSuccess: () => {
      // Invalidate and refetch invoices list
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/invoices'],
      });
    },
  });
}
