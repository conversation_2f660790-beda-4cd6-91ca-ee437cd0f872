import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for updating an existing collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates related queries on success to ensure
 * the UI shows the updated hub data.
 * 
 * Only admins can update collaboration hubs.
 */
export function useUpdateCollaborationHub() {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/hubs/{id}', {
    onSuccess: (_, variables) => {
      // Invalidate and refetch collaboration hubs list
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs'],
      });
      
      // Invalidate and refetch the specific hub
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{id}', { params: { path: { id: variables.params.path.id } } }],
      });
    },
  });
}
