import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for creating a new collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the collaboration hubs list query on success
 * to ensure the UI shows the newly created hub.
 * 
 * The backend automatically:
 * - Scopes the hub to the current account (multi-tenancy)
 * - Adds the creator as an admin participant
 * - Creates default chat channels
 */
export function useCreateCollaborationHub() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/hubs', {
    onSuccess: () => {
      // Invalidate and refetch collaboration hubs list
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs'],
      });
    },
  });
}
