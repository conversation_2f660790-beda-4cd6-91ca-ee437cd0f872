import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for creating a new brand.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates the brands list query on success
 * to ensure the UI shows the newly created brand.
 */
export function useCreateBrand() {
  const queryClient = useQueryClient();

  return $api.useMutation('post', '/api/brands', {
    onSuccess: () => {
      // Invalidate and refetch brands list
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/brands'],
      });
    },
  });
}
