import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Custom hook for updating an existing brand.
 * Uses openapi-react-query for type-safe API calls with React Query.
 * 
 * Automatically invalidates both the brands list and the specific brand query
 * on success to ensure the UI shows the updated data.
 */
export function useUpdateBrand() {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/brands/{id}', {
    onSuccess: (_, variables) => {
      // Invalidate and refetch brands list
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/brands'],
      });
      
      // Invalidate and refetch the specific brand
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/brands/{id}', { id: variables.params.path.id }],
      });
    },
  });
}
