/**
 * Filter System Initialization
 * 
 * Initializes the high-performance event-driven filter system
 * Call this once when the app starts
 */

import { initializeFilterFromUrl } from '@/hooks/posts/use-post-filters'

/**
 * Initialize the filter system
 * Should be called once when the app starts
 */
export function initializeFilterSystem(): void {
  // Initialize filter from URL on app start
  initializeFilterFromUrl()
  
  // Make filter event bus globally accessible for debugging
  if (typeof window !== 'undefined') {
    // This is for development/debugging purposes
    ;(window as any).filterEventBus = {
      getCurrentFilter: () => {
        const { PostFilterEventBus } = require('@/hooks/posts/use-post-filters')
        return PostFilterEventBus.getInstance().getCurrentFilter()
      },
      setFilter: (filter: string) => {
        const { PostFilterEventBus } = require('@/hooks/posts/use-post-filters')
        PostFilterEventBus.getInstance().setFilter(filter)
      },
      subscribe: (callback: (filter: string) => void) => {
        const { PostFilterEventBus } = require('@/hooks/posts/use-post-filters')
        return PostFilterEventBus.getInstance().subscribe(callback)
      }
    }
  }
}
