import { Client, type IMessage, StompConfig } from '@stomp/stompjs';
import SockJS from 'sockjs-client';

export interface ChatMessage {
  id: number;
  content: string;
  sender: {
    id: number;
    name: string;
    email: string;
    role: string;
    is_external: boolean;
  };
  mentions?: Array<{
    name: string;
    email?: string;
  }>;
  attachments?: Array<{
    url: string;
    filename: string;
    content_type: string;
    size: number;
    type: string;
  }>;
  created_at: string;
  updated_at?: string;
  edited_at?: string;
  is_edited: boolean;
}

export interface TypingIndicator {
  type: 'typing';
  participant_id: number;
  participant_email: string;
  participant_name?: string;
  timestamp: string;
}

export interface UserPresence {
  type: 'user_joined';
  participant_id: number;
  participant_email: string;
  timestamp: string;
}

export type WebSocketMessage = ChatMessage | TypingIndicator | UserPresence;

export interface ChatWebSocketClientConfig {
  accessToken: string;
  onMessage?: (channelId: number, message: ChatMessage) => void;
  onTyping?: (channelId: number, typing: TypingIndicator) => void;
  onUserPresence?: (channelId: number, presence: UserPresence) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: unknown) => void;
}

export class ChatWebSocketClient {
  private client: Client;
  private config: ChatWebSocketClientConfig;
  private subscribedChannels = new Set<number>();

  constructor(config: ChatWebSocketClientConfig) {
    this.config = config;

    const stompConfig: StompConfig = {
      webSocketFactory: () => new SockJS('/ws/chat'),
      connectHeaders: {
        Authorization: `Bearer ${config.accessToken}`,
      },
      debug: (str) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('STOMP Debug:', str);
        }
      },
      reconnectDelay: 5000,
      heartbeatIncoming: 4000,
      heartbeatOutgoing: 4000,
      onConnect: () => {
        console.log('WebSocket connected');
        this.config.onConnect?.();
      },
      onDisconnect: () => {
        console.log('WebSocket disconnected');
        this.config.onDisconnect?.();
      },
      onStompError: (frame) => {
        console.error('STOMP error:', frame);
        this.config.onError?.(frame);
      },
      onWebSocketError: (error) => {
        console.error('WebSocket error:', error);
        this.config.onError?.(error);
      },
    };

    this.client = new Client(stompConfig);
  }

  connect(): void {
    this.client.activate();
  }

  disconnect(): void {
    this.client.deactivate();
  }

  subscribeToChannel(channelId: number): void {
    if (this.subscribedChannels.has(channelId)) {
      return; // Already subscribed
    }

    if (!this.client.connected) {
      console.warn('WebSocket not connected, cannot subscribe to channel');
      return;
    }

    // Subscribe to messages for this channel
    this.client.subscribe(`/topic/chat/channel/${channelId}`, (message: IMessage) => {
      try {
        const data = JSON.parse(message.body) as ChatMessage;
        this.config.onMessage?.(channelId, data);
      } catch (error) {
        console.error('Failed to parse message:', error);
      }
    });

    // Subscribe to typing indicators
    this.client.subscribe(`/topic/chat/channel/${channelId}/typing`, (message: IMessage) => {
      try {
        const data = JSON.parse(message.body) as TypingIndicator;
        this.config.onTyping?.(channelId, data);
      } catch (error) {
        console.error('Failed to parse typing indicator:', error);
      }
    });

    // Subscribe to user presence
    this.client.subscribe(`/topic/chat/channel/${channelId}/presence`, (message: IMessage) => {
      try {
        const data = JSON.parse(message.body) as UserPresence;
        this.config.onUserPresence?.(channelId, data);
      } catch (error) {
        console.error('Failed to parse user presence:', error);
      }
    });

    this.subscribedChannels.add(channelId);

    // Send join notification
    this.client.publish({
      destination: `/app/chat/${channelId}/join`,
      body: JSON.stringify({}),
    });
  }

  unsubscribeFromChannel(channelId: number): void {
    if (!this.subscribedChannels.has(channelId)) {
      return; // Not subscribed
    }

    // Note: STOMP client doesn't provide easy way to unsubscribe from specific destinations
    // In a production app, you might want to track subscription objects and unsubscribe them
    this.subscribedChannels.delete(channelId);
  }

  sendMessage(channelId: number, content: string, attachmentUris: string[] = []): void {
    if (!this.client.connected) {
      throw new Error('WebSocket not connected');
    }

    this.client.publish({
      destination: `/app/chat/${channelId}/send`,
      body: JSON.stringify({
        content,
        attachment_uris: attachmentUris,
      }),
    });
  }

  sendTypingIndicator(channelId: number): void {
    if (!this.client.connected) {
      return; // Silently fail for typing indicators
    }

    this.client.publish({
      destination: `/app/chat/${channelId}/typing`,
      body: JSON.stringify({}),
    });
  }

  isConnected(): boolean {
    return this.client.connected;
  }

  updateAccessToken(accessToken: string): void {
    this.config.accessToken = accessToken;
    // Note: STOMP client doesn't support updating headers after connection
    // You would need to disconnect and reconnect with new token
    if (this.client.connected) {
      this.disconnect();
      // Update the connect headers
      this.client.connectHeaders = {
        Authorization: `Bearer ${accessToken}`,
      };
      this.connect();
    }
  }
}
