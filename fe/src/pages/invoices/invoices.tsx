import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router';
import { FileText, Plus, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CardSkeletonGrid } from '@/components/ui/card-skeleton';
import { ResponsiveCardGrid } from '@/components/ui/responsive-card-grid';
import { InvoiceCard } from '@/components/invoice/invoice-card';
import { InvoiceFilters } from '@/components/invoice/invoice-filters';
import { DeleteInvoiceDialog } from '@/components/invoice/delete-invoice-dialog';
import { SendConfirmationDialog } from '@/components/invoice/send-confirmation-dialog';
import { StatusUpdateDialog } from '@/components/invoice/status-update-dialog';
import { InvoiceFormDialog } from '@/components/invoice/invoice-form-dialog';
import { InvoiceViewDialog } from '@/components/invoice/invoice-view-dialog';
import { useInvoices, useInvoicePdf, useSendInvoice, useHasInvoiceBeenSent } from '@/hooks/invoices';
import { usePermissions } from '@/hooks/use-permissions';
import { useTranslations } from '@/lib/i18n/typed-translations';
import type { InvoiceDisplayData, InvoiceFilters as IInvoiceFilters } from '@/components/invoice/types';
import { convertToDisplayData, type InvoiceStatus } from '@/components/invoice/types';
import type { PathsApiInvoicesGetParametersQueryStatus } from '@/lib/api/v1';

export default function InvoicesPage() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [editingInvoice, setEditingInvoice] = useState<InvoiceDisplayData | null>(null);
  const [deletingInvoice, setDeletingInvoice] = useState<InvoiceDisplayData | null>(null);
  const [sendingInvoice, setSendingInvoice] = useState<InvoiceDisplayData | null>(null);
  const [viewingInvoice, setViewingInvoice] = useState<InvoiceDisplayData | null>(null);
  const [statusUpdatingInvoice, setStatusUpdatingInvoice] = useState<InvoiceDisplayData | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const { t, keys } = useTranslations();
  const { canManageInvoices } = usePermissions();

  // Parse filters from URL
  const getFiltersFromUrl = useCallback((): IInvoiceFilters => {
    return {
      status: (searchParams.get('status') as InvoiceStatus) || undefined,
      fromDate: searchParams.get('fromDate') || undefined,
      toDate: searchParams.get('toDate') || undefined,
      page: parseInt(searchParams.get('page') || '0'),
      size: parseInt(searchParams.get('size') || '20'),
    };
  }, [searchParams]);

  const [filters, setFilters] = useState<IInvoiceFilters>(getFiltersFromUrl());

  // Update URL when filters change
  const updateFiltersInUrl = (newFilters: IInvoiceFilters) => {
    const params = new URLSearchParams();
    
    if (newFilters.status) params.set('status', newFilters.status);
    if (newFilters.fromDate) params.set('fromDate', newFilters.fromDate);
    if (newFilters.toDate) params.set('toDate', newFilters.toDate);
    if (newFilters.page && newFilters.page > 0) params.set('page', newFilters.page.toString());
    if (newFilters.size && newFilters.size !== 20) params.set('size', newFilters.size.toString());
    
    setSearchParams(params);
    setFilters(newFilters);
  };

  // Sync filters with URL on mount
  useEffect(() => {
    const urlFilters = getFiltersFromUrl();
    setFilters(urlFilters);
  }, [searchParams, getFiltersFromUrl]);

  const { data: invoicesResponse, isLoading, error, isError, refetch } = useInvoices({
    status: filters.status as unknown as PathsApiInvoicesGetParametersQueryStatus, // Type conversion needed - both enums have same values
    fromDate: filters.fromDate,
    toDate: filters.toDate,
    page: filters.page,
    size: filters.size,
  });

  const { downloadPdf } = useInvoicePdf();
  const sendInvoiceMutation = useSendInvoice();

  // Check if the invoice being sent has already been sent
  const { data: hasBeenSent, isLoading: isCheckingSentStatus } = useHasInvoiceBeenSent(
    sendingInvoice?.id || null,
    { enabled: !!sendingInvoice }
  );

  const handleCreateNew = () => {
    setIsCreating(true);
    setEditingInvoice(null);
  };

  const handleEdit = (invoice: InvoiceDisplayData) => {
    setIsCreating(false);
    setEditingInvoice(invoice);
  };

  const handleDelete = (invoice: InvoiceDisplayData) => {
    setDeletingInvoice(invoice);
  };

  const handleSend = (invoice: InvoiceDisplayData) => {
    setSendingInvoice(invoice);
  };

  const handleSendConfirm = async (force: boolean) => {
    if (!sendingInvoice) return;

    try {
      await sendInvoiceMutation.mutateAsync({
        params: { path: { id: sendingInvoice.id }, query: { force } },
      });

      setSendingInvoice(null);
      handleSuccess();
    } catch (error) {
      console.error('Failed to send invoice:', error);
      // Error handling is managed by the mutation hook
    }
  };

  const handleDownload = async (invoice: InvoiceDisplayData) => {
    try {
      await downloadPdf({
        id: invoice.id
      });
    } catch (error) {
      console.error('Failed to download PDF:', error);
      // Error is already handled by the hook and logged
    }
  };

  const handleViewDetails = (invoice: InvoiceDisplayData) => {
    setViewingInvoice(invoice);
  };

  const handleUpdateStatus = (invoice: InvoiceDisplayData) => {
    setStatusUpdatingInvoice(invoice);
  };

  const handleFiltersChange = (newFilters: IInvoiceFilters) => {
    updateFiltersInUrl({ ...newFilters, page: 0 }); // Reset to first page when filters change
  };

  const handlePageChange = (newPage: number) => {
    updateFiltersInUrl({ ...filters, page: newPage });
  };

  const handleSuccess = () => {
    // Dialogs will close automatically via their onSuccess handlers
    // Data will be automatically refreshed via React Query cache invalidation
  };

  const handleCloseFormDialog = () => {
    setIsCreating(false);
    setEditingInvoice(null);
  };

  if (isLoading) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">
              {t(keys.invoices.title)}
            </h1>
            <p className="text-muted-foreground">
              {t(keys.invoices.description)}
            </p>
          </div>
          {canManageInvoices() && (
            <Button onClick={handleCreateNew} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              {t(keys.invoices.createNew)}
            </Button>
          )}
        </div>

        {/* Filters */}
        <InvoiceFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
        />

        <CardSkeletonGrid count={6} />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-1 flex-col gap-4 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">
              {t(keys.invoices.title)}
            </h1>
            <p className="text-muted-foreground">
              {t(keys.invoices.description)}
            </p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error?.error?.message || t(keys.invoices.error)}
          </AlertDescription>
        </Alert>

        <div className="flex justify-center">
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const invoices = invoicesResponse?.content?.map(convertToDisplayData) || [];
  const hasInvoices = invoices.length > 0;
  const totalPages = invoicesResponse?.total_pages || 0;
  const currentPage = invoicesResponse?.page || 0;

  return (
    <div className="flex flex-1 flex-col gap-4 p-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">
            {t(keys.invoices.title)}
          </h1>
          <p className="text-muted-foreground">
            {t(keys.invoices.description)}
          </p>
        </div>
        {canManageInvoices() && (
          <Button onClick={handleCreateNew} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            {t(keys.invoices.createNew)}
          </Button>
        )}
      </div>

      {/* Filters */}
      <InvoiceFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
      />

      {hasInvoices ? (
        <>
          <ResponsiveCardGrid>
            {invoices.map((invoice) => (
              <InvoiceCard
                key={invoice.id}
                invoice={invoice}
                onEdit={() => handleEdit(invoice)}
                onDelete={() => handleDelete(invoice)}
                onSend={() => handleSend(invoice)}
                onDownload={() => handleDownload(invoice)}
                onViewDetails={() => handleViewDetails(invoice)}
                onUpdateStatus={() => handleUpdateStatus(invoice)}
              />
            ))}
          </ResponsiveCardGrid>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-6">
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === 0}
                onClick={() => handlePageChange(currentPage - 1)}
              >
                {t(keys.invoices.previous)}
              </Button>

              <span className="text-sm text-muted-foreground px-4">
                {t(keys.invoices.page)} {currentPage + 1} {t(keys.invoices.of)} {totalPages}
              </span>

              <Button
                variant="outline"
                size="sm"
                disabled={currentPage >= totalPages - 1}
                onClick={() => handlePageChange(currentPage + 1)}
              >
                {t(keys.invoices.next)}
              </Button>
            </div>
          )}
        </>
      ) : (
        <div className="flex flex-col items-center justify-center min-h-[500px] text-center max-w-2xl mx-auto px-4">
          <div className="mb-6 p-4 bg-muted rounded-full">
            <FileText className="h-12 w-12 text-muted-foreground" />
          </div>
          <h2 className="text-2xl font-semibold mb-3">
            {t(keys.invoices.noInvoices)}
          </h2>
          <p className="text-muted-foreground text-lg mb-8 leading-relaxed">
            {t(keys.invoices.noInvoicesDescription)}
          </p>
          {canManageInvoices() && (
            <Button onClick={handleCreateNew} className="flex items-center gap-2" size="lg">
              <Plus className="h-5 w-5" />
              {t(keys.invoices.createNew)}
            </Button>
          )}
        </div>
      )}

      {/* Dialogs */}
      <DeleteInvoiceDialog
        invoice={deletingInvoice}
        open={!!deletingInvoice}
        onClose={() => setDeletingInvoice(null)}
        onSuccess={handleSuccess}
      />

      <SendConfirmationDialog
        invoice={sendingInvoice}
        open={!!sendingInvoice && !isCheckingSentStatus}
        onClose={() => setSendingInvoice(null)}
        onConfirm={handleSendConfirm}
        isAlreadySent={hasBeenSent || false}
        isPending={sendInvoiceMutation.isPending}
      />

      <StatusUpdateDialog
        invoice={statusUpdatingInvoice}
        open={!!statusUpdatingInvoice}
        onClose={() => setStatusUpdatingInvoice(null)}
        onSuccess={handleSuccess}
      />

      {/* Invoice Form Dialog */}
      <InvoiceFormDialog
        invoiceId={editingInvoice?.id}
        open={isCreating || !!editingInvoice}
        onClose={handleCloseFormDialog}
        onSuccess={handleSuccess}
      />

      {/* Invoice View Dialog */}
      <InvoiceViewDialog
        invoice={viewingInvoice}
        open={!!viewingInvoice}
        onClose={() => setViewingInvoice(null)}
      />
    </div>
  );
}
