import { createBrowserRouter, Navigate } from 'react-router';
import { ROUTES } from './routes';

// Layouts and Auth Components
import CenteredLayout from '@/layouts/centered-layout';
import SidebarLayout from '@/layouts/sidebar-layout';
import ProtectedRoute from '@/components/protected-route';

// Pages
import LoginPage from '@/pages/login/login';
import SignUpPage from '@/pages/sign-up/sign-up';
import MagicLinkAuthPage from '@/pages/auth/magic-link-auth';
import VerifyEmailPage from '@/pages/auth/verify-email';
import DashboardPage from '@/pages/dashboard/dashboard';
import NotFoundPage from '@/pages/not-found/not-found.tsx';
import AuthInitializer from '@/components/auth-initializer.tsx';
import AccountCompaniesPage from '@/pages/account-companies/account-companies.tsx';
import BankDetailsPage from '@/pages/bank-details/bank-details.tsx';
import BrandsPage from '@/pages/brands/brands.tsx';
import { CollaborationHubsPage, CollaborationHubDetailPage } from '@/pages/collaboration-hubs';
import InvoicesPage from '@/pages/invoices/invoices.tsx';

export const router = createBrowserRouter([
  {
    Component: AuthInitializer,
    children: [
      {
        path: '/',
        element: <CenteredLayout />,
        children: [
          {
            index: true,
            element: <Navigate to={ROUTES.LOGIN} replace />,
          },
          {
            path: ROUTES.LOGIN.substring(1), // Remove leading slash
            element: <LoginPage />,
          },
          {
            path: ROUTES.SIGNUP.substring(1), // Remove leading slash
            element: <SignUpPage />,
          },
          {
            path: ROUTES.MAGIC_LINK_AUTH.substring(1), // Remove leading slash
            element: <MagicLinkAuthPage />,
          },
          {
            path: ROUTES.VERIFY_EMAIL.substring(1), // Remove leading slash
            element: <VerifyEmailPage />,
          },
        ],
      },
      {
        path: ROUTES.APP,
        element: (
          <ProtectedRoute>
            <SidebarLayout />
          </ProtectedRoute>
        ),
        children: [
          {
            path: 'dashboard',
            element: <DashboardPage />,
          },
          {
            path: "account-companies",
            element: <AccountCompaniesPage />
          },
          {
            path: "bank-details",
            element: <BankDetailsPage />
          },
          {
            path: "brands",
            element: <BrandsPage />
          },
          {
            path: "collaboration-hubs",
            element: <CollaborationHubsPage />
          },
          {
            path: "collaboration-hubs/:id",
            element: <CollaborationHubDetailPage />
          },
          {
            path: "invoices",
            element: <InvoicesPage />
          },
          {
            path: '*',
            element: <NotFoundPage />,
          }
        ],
      },
      {
        path: ROUTES.NOT_FOUND,
        element: <NotFoundPage />,
      },
    ]
  }
]);