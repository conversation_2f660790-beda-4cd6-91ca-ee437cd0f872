import React from 'react'
import { User, Mail, ExternalLink } from 'lucide-react'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { useTranslations } from '@/lib/i18n/typed-translations'

interface UserProfilePopupProps {
  user: {
    name: string
    email: string
    participant_id: number
    is_external?: boolean
  }
  trigger: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function UserProfilePopup({ user, trigger, open, onOpenChange }: UserProfilePopupProps) {
  const { t, keys } = useTranslations()

  const getInitials = (name?: string) => {
    if (!name) return '?'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getRoleIcon = (isExternal?: boolean) => {
    if (isExternal) {
      return <ExternalLink className="h-4 w-4 text-orange-600" />
    }
    return <User className="h-4 w-4 text-blue-600" />
  }

  const handleEmailClick = () => {
    window.open(`mailto:${user.email}`, '_blank')
  }

  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild>
        {trigger}
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="p-4">
          {/* Header */}
          <div className="flex items-center gap-3 mb-4">
            <Avatar className="h-12 w-12">
              <AvatarFallback className="text-sm font-medium">
                {getInitials(user.name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-base truncate">
                  {user.name || t(keys.ui.userProfilePopup.unknownUser)}
                </h3>
                {getRoleIcon(user.is_external)}
              </div>
              <div className="flex items-center gap-2 mt-1">
                {user.is_external && (
                  <Badge variant="outline" className="text-xs">
                    {t(keys.ui.userProfilePopup.externalUser)}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground truncate">
                {user.email}
              </span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2 mt-4 pt-4 border-t">
            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              onClick={handleEmailClick}
            >
              <Mail className="h-4 w-4 mr-2" />
              {t(keys.ui.userProfilePopup.sendEmail)}
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
