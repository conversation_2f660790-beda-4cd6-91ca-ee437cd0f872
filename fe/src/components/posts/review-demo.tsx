import { PostReviewersSection } from './post-reviewers-section'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'

// Demo data showing how review notes will appear
const mockReviewers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "approved" as const,
    review_notes: "Great work! The content aligns perfectly with our brand guidelines. The messaging is clear and engaging.",
    assigned_at: "2024-01-15T10:00:00Z",
    reviewed_at: "2024-01-15T14:30:00Z"
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>", 
    status: "rework" as const,
    review_notes: "The content is good but needs some adjustments:\n\n1. Please update the CTA to be more specific\n2. The image could be brighter\n3. Consider shortening the first paragraph\n\nOverall direction is great, just needs these tweaks!",
    assigned_at: "2024-01-15T10:00:00Z",
    reviewed_at: "2024-01-15T16:45:00Z"
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    status: "pending" as const,
    assigned_at: "2024-01-15T10:00:00Z"
  }
]

export function ReviewDemo() {
  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <h2 className="text-2xl font-bold">Enhanced Review Display Demo</h2>
      
      {/* Compact View (as shown in PostCard) */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Compact View (Post Card)</h3>
          <p className="text-sm text-muted-foreground">
            This is how reviews appear in the post cards list
          </p>
        </CardHeader>
        <CardContent>
          <PostReviewersSection
            reviewers={mockReviewers}
            compact={true}
          />
        </CardContent>
      </Card>

      {/* Full View (as shown in PostDialog) */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Full View (Post Dialog)</h3>
          <p className="text-sm text-muted-foreground">
            This is how reviews appear in the detailed post dialog
          </p>
        </CardHeader>
        <CardContent>
          <PostReviewersSection
            reviewers={mockReviewers}
            showTimeline={true}
          />
        </CardContent>
      </Card>
    </div>
  )
}
