import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Trash2 } from "lucide-react"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useUpdateComment, useDeleteComment } from "@/hooks/comments"
import { useTranslations } from "@/lib/i18n/typed-translations"
import { UserProfilePopup } from "@/components/ui/user-profile-popup"
import { toast } from "sonner"
import { formatDistanceToNow } from "date-fns"
import type { PostCommentItem } from "@/lib/types/api"


interface CommentItemProps {
  comment: PostCommentItem
  className?: string
}

export function CommentItem({ comment, className }: CommentItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(comment.content || "")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [showUserPopup, setShowUserPopup] = useState(false)
  const [selectedMention, setSelectedMention] = useState<{ name: string; email: string } | null>(null)

  const { t, keys } = useTranslations()
  const updateComment = useUpdateComment()
  const deleteComment = useDeleteComment()

  const getInitials = (name?: string) => {
    if (!name) return '??'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatRelativeTime = (dateString?: string) => {
    if (!dateString) return ''
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true })
    } catch {
      return ''
    }
  }

  const handleEdit = () => {
    setEditContent(comment.content || "")
    setIsEditing(true)
  }

  const handleSaveEdit = async () => {
    if (!comment.id || !editContent.trim()) return

    try {
      await updateComment.mutateAsync({
        params: { path: { commentId: comment.id } },
        body: { content: editContent.trim() }
      })
      
      setIsEditing(false)
      toast.success(t(keys.collaborationHubs.posts.comments.commentUpdated))
    } catch (error) {
      console.error('Failed to update comment:', error)
      toast.error(t(keys.collaborationHubs.posts.comments.failedToUpdate))
    }
  }

  const handleCancelEdit = () => {
    setEditContent(comment.content || "")
    setIsEditing(false)
  }

  const renderCommentContent = () => {
    const content = comment.content || '';

    // Check if comment has mentions
    if (!comment.mentions || comment.mentions.length === 0) {
      return <div className="text-sm text-foreground leading-relaxed">{content}</div>;
    }

    // Create a map of email addresses and prefixes to mention objects for quick lookup
    const mentionMap = new Map();
    comment.mentions.forEach((mention: { name: string; email: string }) => {
      // Map by full email address (primary)
      if (mention.email) {
        const email = mention.email.toLowerCase().trim();
        mentionMap.set(email, mention);

        // Map by email prefix (part before @)
        const emailPrefix = mention.email.split('@')[0].toLowerCase();
        mentionMap.set(emailPrefix, mention);
      }

      // Map by participant name (fallback for manually typed mentions)
      if (mention.name) {
        const fullName = mention.name.toLowerCase().trim();
        mentionMap.set(fullName, mention);

        // Map by first name only
        const firstName = fullName.split(' ')[0];
        if (firstName !== fullName) {
          mentionMap.set(firstName, mention);
        }
      }
    });

    // Parse content and replace mentions with styled components
    const parts: Array<{ text: string; isMention: boolean; mention?: { name: string; email: string } }> = [];
    let lastIndex = 0;

    // Find all @mentions in the content - detect email patterns
    const mentionRegex = /@([a-zA-Z0-9._-]+)/g;
    let match;

    while ((match = mentionRegex.exec(content)) !== null) {
      // Add text before mention
      if (match.index > lastIndex) {
        parts.push({
          text: content.slice(lastIndex, match.index),
          isMention: false
        });
      }

      // Extract the email or email prefix from mention
      const mentionEmail = match[1].toLowerCase().trim();
      const mentionData = mentionMap.get(mentionEmail);

      if (mentionData) {
        // Add the mention with display name instead of email
        parts.push({
          text: `@${mentionData.name}`,
          isMention: true,
          mention: mentionData
        });
      } else {
        // Not a valid mention, treat as regular text
        parts.push({
          text: match[0],
          isMention: false
        });
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      parts.push({
        text: content.slice(lastIndex),
        isMention: false
      });
    }

    return (
      <div className="text-sm text-foreground leading-relaxed">
        {parts.map((part, index) =>
          part.isMention ? (
            <UserProfilePopup
              key={index}
              user={part.mention}
              open={showUserPopup && selectedMention?.participant_id === part.mention.participant_id}
              onOpenChange={(open) => {
                setShowUserPopup(open);
                if (!open) setSelectedMention(null);
              }}
              trigger={
                <span
                  className="text-blue-600 dark:text-blue-400 cursor-pointer hover:underline"
                  title={`${part.mention.name} (${part.mention.email})`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedMention(part.mention);
                    setShowUserPopup(true);
                  }}
                >
                  {part.text}
                </span>
              }
            />
          ) : (
            <span key={index}>{part.text}</span>
          )
        )}
      </div>
    );
  };

  const handleDeleteConfirm = async () => {
    if (!comment.id) return

    try {
      await deleteComment.mutateAsync({
        params: { path: { commentId: comment.id } }
      })
      
      toast.success(t(keys.collaborationHubs.posts.comments.commentDeleted))
      setDeleteDialogOpen(false)
    } catch (error) {
      console.error('Failed to delete comment:', error)
      toast.error(t(keys.collaborationHubs.posts.comments.failedToDelete))
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault()
      handleSaveEdit()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleCancelEdit()
    }
  }

  return (
    <>
      <div className={`flex gap-3 group ${className}`}>
        {/* Avatar */}
        <Avatar className="h-8 w-8 flex-shrink-0">
          <AvatarFallback className="text-xs">
            {getInitials(comment.author?.name)}
          </AvatarFallback>
        </Avatar>

        {/* Comment Content */}
        <div className="flex-1 min-w-0">
          {/* Author and timestamp */}
          <div className="flex items-center gap-2 mb-1">
            <span className="font-semibold text-sm text-foreground">
              {comment.author?.name || 'Unknown'}
            </span>
            <span className="text-xs text-muted-foreground">
              {formatRelativeTime(comment.created_at)}
              {comment.updated_at !== comment.created_at && (
                <span className="ml-1">{t(keys.collaborationHubs.posts.comments.edited)}</span>
              )}
            </span>
          </div>

          {/* Comment text or edit form */}
          {isEditing ? (
            <div className="space-y-2">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                onKeyDown={handleKeyDown}
                className="min-h-[60px] text-sm resize-none"
                placeholder={t(keys.collaborationHubs.posts.comments.writeComment)}
                autoFocus
              />
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={handleSaveEdit}
                  disabled={updateComment.isPending || !editContent.trim()}
                >
                  {updateComment.isPending ? t(keys.collaborationHubs.posts.comments.saving) : t(keys.collaborationHubs.posts.comments.save)}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCancelEdit}
                  disabled={updateComment.isPending}
                >
                  {t(keys.collaborationHubs.posts.comments.cancel)}
                </Button>
              </div>
            </div>
          ) : (
            renderCommentContent()
          )}
        </div>

        {/* Actions menu */}
        {(comment.can_edit || comment.can_delete) && !isEditing && (
          <div className="opacity-0 group-hover:opacity-100 md:opacity-0 md:group-hover:opacity-100 transition-opacity">
            <DropdownMenu>
              <DropdownMenuTrigger>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {comment.can_edit && (
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="h-3 w-3 mr-2" />
                    {t(keys.collaborationHubs.posts.comments.edit)}
                  </DropdownMenuItem>
                )}
                {comment.can_delete && (
                  <DropdownMenuItem
                    onClick={() => setDeleteDialogOpen(true)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="h-3 w-3 mr-2" />
                    {t(keys.collaborationHubs.posts.comments.delete)}
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t(keys.collaborationHubs.posts.comments.confirmDeleteComment)}</AlertDialogTitle>
            <AlertDialogDescription>
              {t(keys.collaborationHubs.posts.comments.deleteCommentDescription)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t(keys.collaborationHubs.posts.comments.cancel)}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={deleteComment.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteComment.isPending ? t(keys.collaborationHubs.posts.comments.deleting) : t(keys.collaborationHubs.posts.comments.delete)}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
