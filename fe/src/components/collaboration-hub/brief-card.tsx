import { <PERSON><PERSON><PERSON>, Calendar, <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { useTranslations } from "@/lib/i18n/typed-translations"
import type { CollaborationBriefListItemDto } from '@/lib/types/api.ts';

interface BriefCardProps {
  brief: CollaborationBriefListItemDto
  onEdit: (briefId: number) => void
  onDelete: (briefId: number) => void
}

export function BriefCard({ brief, onEdit, onDelete }: BriefCardProps) {
  const { t, keys } = useTranslations()

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <div className="p-2 bg-primary/10 rounded-lg">
              <BookOpen className="h-5 w-5 text-primary" />
            </div>
            <div className="space-y-2 flex-1">
              <h3 className="font-semibold leading-tight">{brief.title}</h3>
            </div>
          </div>

          {/* Actions Menu - Always visible */}
          <DropdownMenu>
            <DropdownMenuTrigger>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(brief.id)}>
                <Edit className="h-4 w-4 mr-2" />
                {t(keys.collaborationHubs.briefs.actions.edit)}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(brief.id)}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {t(keys.collaborationHubs.briefs.actions.delete)}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground line-clamp-3">
          {brief.bodyPreview || "No content provided."}
        </p>



        {/* Author and dates */}
        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarFallback className="text-xs">
                {getInitials(brief.createdByParticipantName || "Unknown")}
              </AvatarFallback>
            </Avatar>
            <span className="text-xs text-muted-foreground">
              {brief.createdByParticipantName || "Unknown"}
            </span>
          </div>

          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Calendar className="h-3 w-3" />
            <span>
              {brief.updatedAt !== brief.createdAt
                ? `${t(keys.collaborationHubs.briefs.updated)} ${formatDate(brief.updatedAt)}`
                : `${t(keys.collaborationHubs.briefs.created)} ${formatDate(brief.createdAt)}`
              }
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
