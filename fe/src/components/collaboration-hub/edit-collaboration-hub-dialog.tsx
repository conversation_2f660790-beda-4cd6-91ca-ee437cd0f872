import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useUpdateCollaborationHub, useCollaborationHub } from "@/hooks/collaboration-hubs"
import { useBrands } from "@/hooks/brands"
import { useTranslations, type UseTranslationsReturn } from "@/lib/i18n/typed-translations"
import { useIsMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"
import { toast } from "sonner"
import { Loader2 } from "lucide-react"

const editHubSchema = (t: UseTranslationsReturn['t'], keys: UseTranslationsReturn['keys']) => z.object({
  name: z.string()
    .min(1, t(keys.collaborationHubs.createDialog.validation.hubNameRequired))
    .max(255, t(keys.collaborationHubs.createDialog.validation.hubNameTooLong)),
  brandId: z.number({ required_error: t(keys.collaborationHubs.createDialog.validation.brandRequired) }),
  description: z.string()
    .max(1000, t(keys.collaborationHubs.createDialog.validation.descriptionTooLong))
    .optional(),
})

type EditHubFormData = {
  name: string
  brandId: number
  description?: string
}

interface EditCollaborationHubDialogProps {
  hubId: number | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function EditCollaborationHubDialog({ 
  hubId, 
  open, 
  onOpenChange, 
  onSuccess 
}: EditCollaborationHubDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()

  const form = useForm<EditHubFormData>({
    resolver: zodResolver(editHubSchema(t, keys)),
    defaultValues: {
      name: "",
      description: "",
    },
  })

  const updateMutation = useUpdateCollaborationHub()
  const { data: brandsResponse } = useBrands()
  
  // Fetch hub data when dialog opens
  const { data: hubResponse, isLoading: isLoadingHub } = useCollaborationHub(hubId!, {
    enabled: !!hubId && open
  })

  // Reset form when hub data loads
  useEffect(() => {
    if (hubResponse && open) {
      form.reset({
        name: hubResponse.name,
        brandId: hubResponse.brandId,
        description: hubResponse.description || "",
      })
    }
  }, [hubResponse, form, open])

  const onSubmit = async (data: EditHubFormData) => {
    if (!hubId) return
    
    setIsSubmitting(true)
    try {
      await updateMutation.mutateAsync({
        params: { path: { id: hubId } },
        body: {
          name: data.name,
          description: data.description || undefined,
        },
      })

      toast.success(t(keys.collaborationHubs.editDialog.successMessage))
      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to update collaboration hub:", error)
      toast.error(t(keys.collaborationHubs.editDialog.errorMessage))
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && !isSubmitting) {
      form.reset()
    }
    onOpenChange(newOpen)
  }

  if (!hubId) return null

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className={cn(
        "w-[calc(100vw-1rem)] max-w-[500px] mx-2 sm:mx-auto",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0 mx-0"
      )}>
        <DialogHeader className={cn(
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            isMobile && "text-base"
          )}>
            {t(keys.collaborationHubs.editDialog.title)}
          </DialogTitle>
          <DialogDescription className={cn(
            isMobile && "text-xs"
          )}>
            {t(keys.collaborationHubs.editDialog.description)}
          </DialogDescription>
        </DialogHeader>

        {isLoadingHub ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        ) : (
          <ScrollArea className={cn(
            "max-h-[60vh]",
            isMobile && "max-h-[calc(100dvh-200px)] px-4"
          )}>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className={cn(isMobile && "text-sm")}>
                        {t(keys.collaborationHubs.createDialog.hubName)}
                      </FormLabel>
                      <FormControl>
                        <Input 
                          placeholder={t(keys.collaborationHubs.createDialog.hubNamePlaceholder)}
                          className={cn(isMobile && "min-h-[44px]")}
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage className={cn(isMobile && "text-xs")} />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="brandId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className={cn(isMobile && "text-sm")}>
                        {t(keys.collaborationHubs.createDialog.brand)}
                      </FormLabel>
                      <Select 
                        onValueChange={(value) => field.onChange(parseInt(value))} 
                        value={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger className={cn(isMobile && "min-h-[44px]")}>
                            <SelectValue placeholder={t(keys.collaborationHubs.createDialog.selectBrand)} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {brandsResponse?.content?.map((brand) => (
                            <SelectItem key={brand.id} value={brand.id.toString()}>
                              {brand.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage className={cn(isMobile && "text-xs")} />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className={cn(isMobile && "text-sm")}>
                        {t(keys.collaborationHubs.createDialog.description)}
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t(keys.collaborationHubs.createDialog.descriptionPlaceholder)}
                          className={cn("resize-none", isMobile && "min-h-[88px]")}
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className={cn(isMobile && "text-xs")} />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </ScrollArea>
        )}

        <DialogFooter className={cn(
          "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-2",
          isMobile && "pt-2 pb-4 px-4"
        )}>
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={isSubmitting || isLoadingHub}
            className="min-h-[44px]"
          >
            {t(keys.collaborationHubs.createDialog.cancel)}
          </Button>
          <Button
            type="submit"
            onClick={form.handleSubmit(onSubmit)}
            disabled={isSubmitting || isLoadingHub}
            className="min-h-[44px]"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t(keys.collaborationHubs.editDialog.updating)}
              </>
            ) : (
              t(keys.collaborationHubs.editDialog.updateHub)
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
