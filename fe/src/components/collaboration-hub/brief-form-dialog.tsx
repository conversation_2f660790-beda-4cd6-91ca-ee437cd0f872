import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

import { ScrollArea } from "@/components/ui/scroll-area"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, Loader2 } from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"
import { useTranslations, type UseTranslationsReturn } from "@/lib/i18n/typed-translations"
import { useCreateBrief, useUpdateBrief, useBrief } from "@/hooks/collaboration-hub-briefs"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

// Form validation schema
const briefFormSchema = (t: UseTranslationsReturn['t'], keys: UseTranslationsReturn['keys']) => z.object({
  title: z.string()
    .min(1, t(keys.collaborationHubs.briefs.validation.titleRequired))
    .max(255, t(keys.collaborationHubs.briefs.validation.titleMaxLength)),
  body: z.string()
    .max(10000, t(keys.collaborationHubs.briefs.validation.bodyMaxLength))
    .optional(),
})

type BriefFormValues = z.infer<ReturnType<typeof briefFormSchema>>

interface BriefFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  hubId: number
  briefId?: number | null
  onSuccess?: () => void
}

export function BriefFormDialog({
  open,
  onOpenChange,
  hubId,
  briefId = null,
  onSuccess
}: BriefFormDialogProps) {
  const isMobile = useIsMobile()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { t, keys } = useTranslations()

  const isEditing = !!briefId

  // Hooks for API operations
  const createBriefMutation = useCreateBrief()
  const updateBriefMutation = useUpdateBrief()

  // Fetch existing brief data if editing
  const { data: existingBrief, isLoading: isLoadingBrief } = useBrief(hubId, briefId, {
    enabled: isEditing && open
  })



  const form = useForm<BriefFormValues>({
    resolver: zodResolver(briefFormSchema(t, keys)),
    defaultValues: {
      title: "",
      body: "",
    },
  })

  // Reset form when dialog opens/closes or when editing different brief
  useEffect(() => {
    if (open) {
      if (isEditing && existingBrief) {
        // Populate form with existing brief data
        form.reset({
          title: existingBrief.title || "",
          body: existingBrief.body || "",
        })
      } else if (!isEditing) {
        // Reset to empty form for creating new brief
        form.reset({
          title: "",
          body: "",
        })
      }
    }
  }, [open, isEditing, existingBrief, form])

  const handleSubmit = async (data: BriefFormValues) => {
    setIsSubmitting(true)
    
    try {
      if (isEditing && briefId) {
        await updateBriefMutation.mutateAsync({
          params: {
            path: { hubId, briefId }
          },
          body: {
            title: data.title,
            body: data.body || undefined,
          },
        })

        toast.success(t(keys.collaborationHubs.briefs.dialog.updateSuccess))
      } else {
        await createBriefMutation.mutateAsync({
          params: {
            path: { hubId }
          },
          body: {
            title: data.title,
            body: data.body || undefined,
          },
        })

        toast.success(t(keys.collaborationHubs.briefs.dialog.createSuccess))
      }
      
      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      // Error handling is managed by the mutation hooks and will show toast
      console.error('Failed to save brief:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const isPending = createBriefMutation.isPending || updateBriefMutation.isPending || isSubmitting
  const hasError = createBriefMutation.isError || updateBriefMutation.isError

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn(
        "w-[calc(100vw-1rem)] max-w-2xl max-h-[calc(100vh-2rem)] flex flex-col mx-2",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0 mx-0"
      )}>
        <DialogHeader className={cn(
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <DialogTitle className={cn(
            isMobile && "text-base"
          )}>
            {isEditing
              ? t(keys.collaborationHubs.briefs.dialog.editTitle)
              : t(keys.collaborationHubs.briefs.dialog.createTitle)
            }
          </DialogTitle>
          <DialogDescription className={cn(
            isMobile && "text-xs"
          )}>
            {isEditing
              ? "Update the brief information below."
              : "Create a new brief to share guidelines, workflows, and important information with your team."
            }
          </DialogDescription>
        </DialogHeader>

        <div className={cn(isMobile && "px-4")}>
          {/* Loading state for fetching existing brief */}
          {isEditing && isLoadingBrief && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading brief...</span>
            </div>
          )}

          {/* Error state */}
          {hasError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {isEditing
                  ? "Failed to update brief. Please try again."
                  : "Failed to create brief. Please try again."
                }
              </AlertDescription>
            </Alert>
          )}
        </div>

        <ScrollArea className={cn(
          "flex-1 mt-4",
          isMobile && "px-4"
        )}>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className={cn(
              "space-y-6 px-1",
              isMobile && "px-0"
            )}>
              
              {/* Title Field */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t(keys.collaborationHubs.briefs.dialog.titleLabel)}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t(keys.collaborationHubs.briefs.dialog.titlePlaceholder)}
                        {...field}
                        disabled={isPending || (isEditing && isLoadingBrief)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Body Field */}
              <FormField
                control={form.control}
                name="body"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t(keys.collaborationHubs.briefs.dialog.bodyLabel)}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t(keys.collaborationHubs.briefs.dialog.bodyPlaceholder)}
                        className="min-h-[120px] resize-none"
                        {...field}
                        disabled={isPending || (isEditing && isLoadingBrief)}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional content for the brief. You can include guidelines, workflows, or any important information.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />



            </form>
          </Form>
        </ScrollArea>

        <DialogFooter className={cn(
          "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-3 pt-4 border-t",
          isMobile && "pb-4 px-4"
        )}>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isPending}
            className="min-h-[44px]"
          >
            {t(keys.collaborationHubs.briefs.dialog.cancel)}
          </Button>
          <Button
            type="submit"
            onClick={form.handleSubmit(handleSubmit)}
            disabled={isPending || (isEditing && isLoadingBrief)}
            className="min-h-[44px]"
          >
            {isPending
              ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {isEditing 
                    ? t(keys.collaborationHubs.briefs.dialog.updating)
                    : t(keys.collaborationHubs.briefs.dialog.creating)
                  }
                </>
              )
              : t(keys.collaborationHubs.briefs.dialog.save)
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
