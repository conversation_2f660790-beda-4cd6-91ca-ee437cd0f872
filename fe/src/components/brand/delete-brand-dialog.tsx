import { Alert<PERSON>riangle } from "lucide-react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useDeleteBrand } from "@/hooks/brands"
import { useTranslations } from '@/lib/i18n/typed-translations'
import { useIsMobile } from '@/hooks/use-mobile'
import { cn } from '@/lib/utils'
import type { Brand } from './types'

interface DeleteBrandDialogProps {
  brand: Brand | null
  open: boolean
  onClose: () => void
  onSuccess: () => void
}

export function DeleteBrandDialog({ brand, open, onClose, onSuccess }: DeleteBrandDialogProps) {
  const { t, keys } = useTranslations()
  const isMobile = useIsMobile()
  const deleteBrand = useDeleteBrand()

  const handleDelete = async () => {
    if (!brand) return

    try {
      await deleteBrand.mutateAsync({
        params: { path: { id: brand.id } }
      })
      
      onSuccess()
      onClose()
    } catch (error) {
      // Error handling is managed by the mutation hook and will show appropriate error messages
      console.error('Failed to delete brand:', error)
    }
  }

  if (!brand) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={cn(
        "w-[calc(100vw-1rem)] max-w-[425px] mx-2 sm:mx-auto",
        isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0 mx-0"
      )}>
        <DialogHeader className={cn(
          isMobile && "pb-2 pt-4 px-4"
        )}>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-destructive/10 rounded-lg">
              <AlertTriangle className={cn("h-5 w-5 text-destructive", isMobile && "h-4 w-4")} />
            </div>
            <div>
              <DialogTitle className={cn(
                isMobile && "text-base"
              )}>{t(keys.brands.confirmDelete)}</DialogTitle>
              <DialogDescription className={cn(
                "mt-1",
                isMobile && "text-xs"
              )}>
                {t(keys.brands.deleteDescription)}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className={cn("py-4", isMobile && "px-4")}>
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="space-y-1">
              <p className="font-medium">{brand.name}</p>
              <p className="text-sm text-muted-foreground">{brand.company_name}</p>
              {brand.contacts.length > 0 && (
                <p className="text-sm text-muted-foreground">
                  {brand.contacts.length === 1
                    ? `${brand.contacts.length} contact will also be deleted`
                    : `${brand.contacts.length} contacts will also be deleted`
                  }
                </p>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className={cn(
          "flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-2",
          isMobile && "pt-2 pb-4 px-4"
        )}>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={deleteBrand.isPending}
            className="min-h-[44px]"
          >
            {t(keys.brands.cancel)}
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={deleteBrand.isPending}
            className="min-h-[44px]"
          >
            {deleteBrand.isPending
              ? t(keys.brands.deleting)
              : t(keys.brands.delete)
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
