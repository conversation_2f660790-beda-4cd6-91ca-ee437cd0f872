import { memo, useEffect } from 'react';
import type { Control, UseFormSetValue, UseFormGetValues } from 'react-hook-form';
import { Wand2, Loader2 } from 'lucide-react';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useNextInvoiceNumber } from '@/hooks/invoices';
import type { InvoiceFormValues } from '../converters';

interface BasicInfoSectionProps {
  control: Control<InvoiceFormValues>;
  isPending: boolean;
  setValue: UseFormSetValue<InvoiceFormValues>;
  getValues: UseFormGetValues<InvoiceFormValues>;
  isEditing?: boolean;
  isDialogOpen?: boolean;
}

const CURRENCY_OPTIONS = [
  { value: 'EUR', label: 'EUR - Euro' },
  { value: 'USD', label: 'USD - US Dollar' },
  { value: 'GBP', label: 'GBP - British Pound' },
] as const;

/**
 * Basic information section for invoice forms.
 *
 * Contains:
 * - Invoice number with generation functionality
 * - Currency selection
 * - Issue date
 * - Due date
 *
 * Memoized to prevent unnecessary re-renders when other form fields change.
 */
export const BasicInfoSection = memo(function BasicInfoSection({
  control,
  isPending,
  setValue,
  getValues,
  isEditing = false,
  isDialogOpen = false,
}: BasicInfoSectionProps) {
  const { t, keys } = useTranslations();

  // Hook for fetching next invoice number
  const {
    data: nextNumberData,
    isLoading: isGeneratingNumber,
    refetch: generateNumber,
  } = useNextInvoiceNumber({
    enabled: !isEditing && isDialogOpen, // Auto-fetch for new invoices when dialog opens
  });

  // Auto-populate invoice number when data is available for new invoices
  useEffect(() => {
    if (!isEditing && isDialogOpen && nextNumberData?.next_invoice_number) {
      const currentInvoiceNumber = getValues('invoice_number');
      // Only set if the field is empty
      if (!currentInvoiceNumber || currentInvoiceNumber.trim() === '') {
        setValue('invoice_number', nextNumberData.next_invoice_number, {
          shouldValidate: false, // Don't validate immediately
          shouldDirty: false, // Don't mark as dirty since it's auto-generated
        });
      }
    }
  }, [isEditing, isDialogOpen, nextNumberData, setValue, getValues]);

  // Handle manual invoice number generation
  const handleGenerateNumber = async () => {
    try {
      const result = await generateNumber();
      if (result.data?.next_invoice_number) {
        setValue('invoice_number', result.data.next_invoice_number, {
          shouldValidate: true,
          shouldDirty: true,
        });
      }
    } catch (error) {
      console.error('Failed to generate invoice number:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">
          {t(keys.invoices.form.basicInfo)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <FormField
            control={control}
            name="invoice_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t(keys.invoices.form.invoiceNumber)}</FormLabel>
                <FormControl>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Input
                        {...field}
                        disabled={isPending || isGeneratingNumber}
                        placeholder={isGeneratingNumber ? t(keys.invoices.form.generatingInvoiceNumber) : t(keys.invoices.form.placeholders.invoiceNumber)}
                        className="h-11"
                      />
                      {isGeneratingNumber && (
                        <div className="absolute right-3 top-1/2 -translate-y-1/2">
                          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    {!isEditing && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleGenerateNumber}
                        disabled={isPending || isGeneratingNumber}
                        className="h-11 px-3 shrink-0"
                        title={t(keys.invoices.form.generateInvoiceNumber)}
                      >
                        <Wand2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={control}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t(keys.invoices.form.currency)}</FormLabel>
                <Select onValueChange={field.onChange} value={field.value} disabled={isPending}>
                  <FormControl>
                    <SelectTrigger className="h-11 w-full">
                      <SelectValue placeholder={t(keys.invoices.form.placeholders.selectCurrency)} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {CURRENCY_OPTIONS.map((currency) => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <FormField
            control={control}
            name="issue_date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t(keys.invoices.form.issueDate)}</FormLabel>
                <FormControl>
                  <DatePicker
                    value={field.value}
                    onChange={field.onChange}
                    disabled={isPending}
                    placeholder={t(keys.invoices.form.placeholders.selectIssueDate)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="due_date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t(keys.invoices.form.dueDate)}</FormLabel>
                <FormControl>
                  <DatePicker
                    value={field.value}
                    onChange={field.onChange}
                    disabled={isPending}
                    placeholder={t(keys.invoices.form.placeholders.selectDueDate)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
});
