import { Send, Mail, CheckCircle, XCircle } from 'lucide-react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useSendInvoice } from '@/hooks/invoices';
import { useTranslations } from '@/lib/i18n/typed-translations';
import type { InvoiceDisplayData } from './types';

interface SendInvoiceDialogProps {
  invoice: InvoiceDisplayData | null;
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function SendInvoiceDialog({ 
  invoice, 
  open, 
  onClose, 
  onSuccess 
}: SendInvoiceDialogProps) {
  const { t, keys } = useTranslations();
  const sendInvoiceMutation = useSendInvoice();

  const handleSend = async () => {
    if (!invoice) return;

    try {
      const result = await sendInvoiceMutation.mutateAsync({
        params: { path: { id: invoice.id } }
      });
      
      if (result.success) {
        onSuccess();
        onClose();
      }
    } catch (error) {
      // Error handling is managed by the mutation hook
      console.error('Failed to send invoice:', error);
    }
  };

  if (!invoice) return null;

  // Mock recipients data - in real implementation, this would come from the invoice details
  const mockRecipients = [
    { email: '<EMAIL>', type: 'original' as const },
    { email: '<EMAIL>', type: 'copy' as const },
  ];

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <Send className="h-5 w-5 text-primary" />
            <DialogTitle>
              {t(keys.invoices.sendDialog.title)}
            </DialogTitle>
          </div>
          <DialogDescription>
            {t(keys.invoices.sendDialog.description)}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Invoice Summary */}
          <div className="rounded-lg border p-4 bg-muted/50">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">
                  {t(keys.invoices.invoiceNumber)}:
                </span>
                <span className="text-sm font-medium">{invoice.invoice_number}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">
                  {t(keys.invoices.recipient)}:
                </span>
                <span className="text-sm font-medium">{invoice.recipient_name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">
                  {t(keys.invoices.totalAmount)}:
                </span>
                <span className="text-sm font-medium">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: invoice.currency,
                  }).format(invoice.total_amount)}
                </span>
              </div>
            </div>
          </div>

          {/* Recipients List */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">
              {t(keys.invoices.sendDialog.recipientsList)}
            </h4>
            <div className="space-y-2">
              {mockRecipients.map((recipient, index) => (
                <div key={index} className="flex items-center justify-between p-2 rounded border">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{recipient.email}</span>
                  </div>
                  <Badge variant={recipient.type === 'original' ? 'default' : 'secondary'}>
                    {recipient.type === 'original'
                      ? (t(keys.invoices.primary))
                      : (t(keys.invoices.cc))
                    }
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* Send Status */}
          {sendInvoiceMutation.isSuccess && (
            <div className="flex items-center gap-2 p-3 rounded-lg bg-green-50 text-green-800 border border-green-200">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">
                {t(keys.invoices.sendDialog.success)}
              </span>
            </div>
          )}

          {sendInvoiceMutation.isError && (
            <div className="flex items-center gap-2 p-3 rounded-lg bg-red-50 text-red-800 border border-red-200">
              <XCircle className="h-4 w-4" />
              <span className="text-sm">
                {t(keys.invoices.sendDialog.error)}
              </span>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={onClose}
            disabled={sendInvoiceMutation.isPending}
          >
            {t(keys.invoices.cancel)}
          </Button>
          <Button 
            onClick={handleSend}
            disabled={sendInvoiceMutation.isPending || sendInvoiceMutation.isSuccess}
          >
            {sendInvoiceMutation.isPending ? (
              <>
                <Send className="mr-2 h-4 w-4 animate-pulse" />
                {t(keys.invoices.sending)}
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                {t(keys.invoices.sendDialog.confirmSend)}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
