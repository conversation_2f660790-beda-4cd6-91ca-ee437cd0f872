import { useState } from 'react';
import { AlertCircle, CheckCircle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useUpdateInvoiceStatus } from '@/hooks/invoices';
import { useTranslations } from '@/lib/i18n/typed-translations';
import type { InvoiceDisplayData } from './types';
import { INVOICE_STATUS_CONFIG } from './types';
import { InvoiceStatusUpdateRequestStatus } from '@/lib/api/v1';

interface StatusUpdateDialogProps {
  invoice: InvoiceDisplayData | null;
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function StatusUpdateDialog({ 
  invoice, 
  open, 
  onClose, 
  onSuccess 
}: StatusUpdateDialogProps) {
  const { t, keys } = useTranslations();
  const [newStatus, setNewStatus] = useState<InvoiceStatusUpdateRequestStatus | ''>('');
  const [note, setNote] = useState('');
  const updateStatusMutation = useUpdateInvoiceStatus();

  const handleStatusUpdate = async () => {
    if (!invoice || !newStatus) return;

    try {
      await updateStatusMutation.mutateAsync({
        params: {
          path: { id: invoice.id }
        },
        body: {
          status: newStatus,
          note: note || undefined,
        }
      });
      
      onSuccess();
      onClose();
      setNewStatus('');
      setNote('');
    } catch (error) {
      console.error('Failed to update invoice status:', error);
    }
  };

  const handleClose = () => {
    onClose();
    setNewStatus('');
    setNote('');
  };

  if (!invoice) return null;

  const currentStatusConfig = INVOICE_STATUS_CONFIG[invoice.status];
  const statusOptions = Object.values(INVOICE_STATUS_CONFIG).filter(
    status => status.value !== invoice.status
  );

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {t(keys.invoices.statusUpdate.title)}
          </DialogTitle>
          <DialogDescription>
            Update the status of invoice {invoice.invoice_number}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Current Status */}
          <div className="space-y-2">
            <Label>
              {t(keys.invoices.statusUpdate.currentStatus)}
            </Label>
            <div className="flex items-center gap-2">
              <Badge variant={currentStatusConfig.color}>
                {currentStatusConfig.label}
              </Badge>
            </div>
          </div>

          {/* New Status Selection */}
          <div className="space-y-2">
            <Label htmlFor="new-status">
              {t(keys.invoices.statusUpdate.newStatus)}
            </Label>
            <Select value={newStatus} onValueChange={(value) => setNewStatus(value as InvoiceStatusUpdateRequestStatus)}>
              <SelectTrigger id="new-status">
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Optional Note */}
          <div className="space-y-2">
            <Label htmlFor="status-note">
              {t(keys.invoices.statusUpdate.note)}
            </Label>
            <Textarea
              id="status-note"
              placeholder="Add a note about this status change..."
              value={note}
              onChange={(e) => setNote(e.target.value)}
              rows={3}
            />
          </div>

          {/* Success/Error Messages */}
          {updateStatusMutation.isSuccess && (
            <div className="flex items-center gap-2 p-3 rounded-lg bg-green-50 text-green-800 border border-green-200">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">Status updated successfully</span>
            </div>
          )}

          {updateStatusMutation.isError && (
            <div className="flex items-center gap-2 p-3 rounded-lg bg-red-50 text-red-800 border border-red-200">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">Failed to update status</span>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={updateStatusMutation.isPending}
          >
            {t(keys.invoices.cancel)}
          </Button>
          <Button
            onClick={handleStatusUpdate}
            disabled={updateStatusMutation.isPending || !newStatus || updateStatusMutation.isSuccess}
          >
            {updateStatusMutation.isPending ? (
              <>
                {t(keys.invoices.statusUpdate.updating)}
              </>
            ) : (
              <>
                {t(keys.invoices.statusUpdate.update)}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
