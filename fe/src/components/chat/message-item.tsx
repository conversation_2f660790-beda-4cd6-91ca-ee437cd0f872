import { useState } from 'react';
import { MoreHorizontal, Edit, Trash2, Download, FileText, Image, Video } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { UserProfilePopup } from '@/components/ui/user-profile-popup';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useAuth } from '@/contexts/auth-context';
import { useDeleteMessage } from '@/hooks/chat';
import { useDownloadAttachment } from '@/hooks/chat/use-download-attachment';
import { cn } from '@/lib/utils';

interface MessageItemProps {
  message: {
    id?: number;
    content?: string;
    sender?: {
      id?: number;
      name?: string;
      email?: string;
      role?: string;
      is_external?: boolean;
    };
    mentions?: Array<{
      name: string;
      email?: string;
    }>;
    attachments?: Array<{
      url: string;
      filename: string;
      content_type?: string;
      size: number;
      type?: string;
    }>;
    created_at?: string;
    updated_at?: string;
    edited_at?: string;
    is_edited?: boolean;
  };
  channelId: number;
  onEdit?: (messageId: number, content: string) => void;
  className?: string;
}

export function MessageItem({ message, channelId, onEdit, className }: MessageItemProps) {
  const { t, keys } = useTranslations();
  const { user } = useAuth();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedMention, setSelectedMention] = useState<{ name: string; email: string } | null>(null);
  const [showUserPopup, setShowUserPopup] = useState(false);

  const deleteMessageMutation = useDeleteMessage(channelId);
  const { downloadAttachment } = useDownloadAttachment();

  // Safety check for message data
  if (!message || !message.id || !message.sender) {
    return null;
  }

  const isOwnMessage = user?.email === message.sender?.email;
  
  const getInitials = (name?: string) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleBadgeColor = (role?: string) => {
    switch (role) {
      case "admin": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "reviewer": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      case "content_creator": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (contentType: string | null | undefined) => {
    if (!contentType) return <FileText className="h-4 w-4" />;
    if (contentType.startsWith('image/')) return <Image className="h-4 w-4" />;
    if (contentType.startsWith('video/')) return <Video className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const handleDelete = async () => {
    if (!message.id) return;

    try {
      await deleteMessageMutation.mutateAsync({
        params: { path: { channelId, messageId: message.id } }
      });
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Failed to delete message:', error);
    }
  };

  const handleEdit = () => {
    if (message.id && message.content) {
      onEdit?.(message.id, message.content);
    }
  };

  const handleDownload = async (attachment: { url: string; filename: string }) => {
    try {
      await downloadAttachment(attachment.url, attachment.filename);
    } catch (error) {
      console.error('Failed to download attachment:', error);
      // You could show a toast notification here
    }
  };

  const renderContent = () => {
    const content = message.content || '';

    if (!message.mentions || message.mentions.length === 0) {
      return <div className="whitespace-pre-wrap break-words">{content}</div>;
    }

    // Parse content and replace mentions with styled components
    const parts: Array<{ text: string; isMention: boolean; mention?: { name: string; email: string } }> = [];
    let lastIndex = 0;

    // Create a map for comprehensive mention lookup (emails, prefixes, names)
    const mentionMap = new Map();
    message.mentions.forEach(mention => {
      // Map by full email address (primary)
      if (mention.email) {
        const email = mention.email.toLowerCase().trim();
        mentionMap.set(email, mention);

        // Map by email prefix (part before @)
        const emailPrefix = mention.email.split('@')[0].toLowerCase();
        mentionMap.set(emailPrefix, mention);
      }

      // Map by participant name (fallback for manually typed mentions)
      if (mention.name) {
        const fullName = mention.name.toLowerCase().trim();
        mentionMap.set(fullName, mention);

        // Map by first name only
        const firstName = fullName.split(' ')[0];
        if (firstName !== fullName) {
          mentionMap.set(firstName, mention);
        }
      }
    });

    // Find all @mentions in the content - detect email prefixes and full emails
    const mentionRegex = /@([a-zA-Z0-9._-]+)/g;
    let match;

    while ((match = mentionRegex.exec(content)) !== null) {
      // Add text before mention
      if (match.index > lastIndex) {
        parts.push({
          text: content.slice(lastIndex, match.index),
          isMention: false
        });
      }

      // Extract the email or email prefix from mention
      const mentionEmail = match[1].toLowerCase().trim();
      const mentionData = mentionMap.get(mentionEmail);

      if (mentionData) {
        // Add the mention with display name instead of email
        parts.push({
          text: `@${mentionData.name}`,
          isMention: true,
          mention: mentionData
        });
      } else {
        // Not a valid mention, treat as regular text
        parts.push({
          text: match[0],
          isMention: false
        });
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      parts.push({
        text: content.slice(lastIndex),
        isMention: false
      });
    }

    return (
      <div className="whitespace-pre-wrap break-words">
        {parts.map((part, index) =>
          part.isMention ? (
            <UserProfilePopup
              key={index}
              user={part.mention}
              open={showUserPopup && selectedMention?.participant_id === part.mention.participant_id}
              onOpenChange={(open) => {
                setShowUserPopup(open);
                if (!open) setSelectedMention(null);
              }}
              trigger={
                <span
                  className="text-blue-600 dark:text-blue-400 cursor-pointer hover:underline"
                  title={`${part.mention.name} (${part.mention.email})`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedMention(part.mention);
                    setShowUserPopup(true);
                  }}
                >
                  {part.text}
                </span>
              }
            />
          ) : (
            <span key={index}>{part.text}</span>
          )
        )}
      </div>
    );
  };

  return (
    <>
      <div className={cn("flex gap-3 group hover:bg-muted/50 p-2 rounded-lg transition-colors", className)}>
        <Avatar className="h-8 w-8 shrink-0">
          <AvatarImage src={undefined} />
          <AvatarFallback className="text-xs">
            {getInitials(message.sender?.name)}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 space-y-1 min-w-0">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="font-medium text-sm">{message.sender?.name || 'Unknown'}</span>
            <Badge className={getRoleBadgeColor(message.sender?.role)}>
              {message.sender?.role?.replace('_', ' ') || 'User'}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {message.created_at ? new Date(message.created_at).toLocaleTimeString() : ''}
            </span>
            {message.is_edited && (
              <span className="text-xs text-muted-foreground">
                ({t(keys.collaborationHubs.chat.edited)})
              </span>
            )}
          </div>
          
          <div className="text-sm">
            {renderContent()}
          </div>

          {/* Attachments */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="space-y-2 mt-2">
              {message.attachments.map((attachment, index) => (
                <div key={index} className="flex items-center gap-2 p-2 border rounded-lg bg-muted/30">
                  {getFileIcon(attachment.content_type || 'application/octet-stream')}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{attachment.filename}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(attachment.size)}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => handleDownload(attachment)}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Message Actions */}
        {isOwnMessage && (
          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
            <DropdownMenu>
              <DropdownMenuTrigger >
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  {t(keys.collaborationHubs.chat.editMessage)}
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {t(keys.collaborationHubs.chat.deleteMessage)}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t(keys.collaborationHubs.chat.confirmDelete)}</AlertDialogTitle>
            <AlertDialogDescription>
              {t(keys.collaborationHubs.chat.deleteDescription)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t(keys.common.cancel)}</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              disabled={deleteMessageMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteMessageMutation.isPending ? t(keys.common.deleting) : t(keys.common.delete)}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
