import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Trash2, User<PERSON><PERSON>, UserMinus, <PERSON>h, Lock, Users } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useTranslations } from '@/lib/i18n/typed-translations';
import { useIsMobile } from '@/hooks/use-mobile';
import { useDeleteChannel } from '@/hooks/chat';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { ChatChannelResponse } from '@/lib/types/api';

interface ChannelManagementModalProps {
  hubId: number;
  channel: ChatChannelResponse;
  isOpen: boolean;
  onClose: () => void;
  onChannelDeleted?: () => void;
  onManageParticipants?: () => void;
}

export function ChannelManagementModal({
  hubId,
  channel,
  isOpen,
  onClose,
  onChannelDeleted,
  onManageParticipants
}: ChannelManagementModalProps) {
  const { t, keys } = useTranslations();
  const isMobile = useIsMobile();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const deleteChannelMutation = useDeleteChannel(hubId);

  const isGeneralChannel = channel.scope === 'general';
  const canManage = channel.can_manage;
  const canDelete = canManage && !isGeneralChannel;

  const handleDeleteChannel = async () => {
    try {
      await deleteChannelMutation.mutateAsync({
        params: { path: { hubId, channelId: channel.id! } }
      });

      toast.success(t(keys.collaborationHubs.chat.channelDeleted));
      setShowDeleteDialog(false);
      onClose();
      
      if (onChannelDeleted) {
        onChannelDeleted();
      }
    } catch (error) {
      console.error('Failed to delete channel:', error);
      toast.error(t(keys.collaborationHubs.chat.failedToDeleteChannel));
    }
  };

  const getChannelIcon = () => {
    if (isGeneralChannel) {
      return <Hash className="h-5 w-5" />;
    }
    return <Lock className="h-5 w-5" />;
  };

  const getChannelTypeLabel = () => {
    return isGeneralChannel 
      ? t(keys.collaborationHubs.chat.generalChannel)
      : t(keys.collaborationHubs.chat.customChannel);
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className={cn(
          "max-w-md",
          isMobile && "h-[100dvh] max-h-[100dvh] w-[100vw] max-w-[100vw] rounded-none p-0"
        )}>
          <DialogHeader className={cn(
            "pb-4",
            isMobile && "pb-2 pt-4 px-4"
          )}>
            <DialogTitle className={cn(
              "flex items-center gap-2",
              isMobile && "text-base"
            )}>
              <Settings className={cn("h-5 w-5", isMobile && "h-4 w-4")} />
              Channel Management
            </DialogTitle>
          </DialogHeader>

          <ScrollArea className={cn(
            "max-h-[60vh]",
            isMobile && "max-h-[calc(100vh-12rem)] px-4"
          )}>
            <div className="space-y-6 pr-4">
              {/* Section 1: Chat Settings */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Chat Settings
                </h3>

                <div className="flex items-center gap-3">
                  {getChannelIcon()}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold truncate">
                      {channel.name}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {getChannelTypeLabel()}
                    </p>
                  </div>
                </div>

                {channel.description && (
                  <p className="text-sm text-muted-foreground">
                    {channel.description}
                  </p>
                )}

                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>{channel.participant_count || 0} {t(keys.collaborationHubs.chat.participants)}</span>
                  {channel.created_at && (
                    <span>Created {new Date(channel.created_at).toLocaleDateString()}</span>
                  )}
                </div>
              </div>

              <Separator />

              {/* Section 2: Manage Participants */}
              {!isGeneralChannel && (
                <>
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      Manage Participants
                    </h3>

                    {canManage ? (
                      <Button
                        variant="outline"
                        className="w-full justify-start"
                        onClick={() => {
                          onClose();
                          if (onManageParticipants) {
                            onManageParticipants();
                          }
                        }}
                      >
                        <UserPlus className="h-4 w-4 mr-2" />
                        {t(keys.collaborationHubs.chat.manageParticipants)}
                      </Button>
                    ) : (
                      <div className="p-3 bg-muted/50 rounded-lg">
                        <p className="text-sm text-muted-foreground">
                          Only channel creators can manage participants
                        </p>
                      </div>
                    )}
                  </div>

                  <Separator />
                </>
              )}

              {/* Section 3: Delete Channel */}
              <div className="space-y-3">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-destructive">
                  <Trash2 className="h-5 w-5" />
                  Delete Channel
                </h3>

                {canDelete ? (
                  <Button
                    variant="outline"
                    className="w-full justify-start text-destructive hover:text-destructive border-destructive/20 hover:border-destructive/30"
                    onClick={() => setShowDeleteDialog(true)}
                    disabled={deleteChannelMutation.isPending}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t(keys.collaborationHubs.chat.deleteChannel)}
                  </Button>
                ) : (
                  <div className="p-3 bg-muted/50 rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      {isGeneralChannel
                        ? t(keys.collaborationHubs.chat.cannotDeleteGeneral)
                        : t(keys.collaborationHubs.chat.onlyCreatorCanDelete)
                      }
                    </p>
                  </div>
                )}
              </div>
            </div>
          </ScrollArea>

          {/* Close Button */}
          <div className={cn(
            "flex justify-end pt-4 border-t",
            isMobile && "px-4 pb-4"
          )}>
            <Button variant="outline" onClick={onClose}>
              {t(keys.common.close)}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t(keys.collaborationHubs.chat.confirmDeleteChannel)}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t(keys.collaborationHubs.chat.deleteChannelDescription)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              {t(keys.common.cancel)}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteChannel}
              disabled={deleteChannelMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteChannelMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                  {t(keys.collaborationHubs.deleting)}
                </>
              ) : (
                t(keys.collaborationHubs.chat.deleteChannel)
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
