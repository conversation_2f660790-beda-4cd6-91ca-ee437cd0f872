<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${subject}">Email</title>
    <style th:fragment="email-styles">
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
        }

        /* Base styles */
        body {
            margin: 0;
            padding: 0;
            width: 100% !important;
            min-width: 100%;
            background-color: #f8fafc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
        }

        .email-logo {
            color: #ffffff;
            font-size: 28px;
            font-weight: bold;
            text-decoration: none;
            margin: 0;
        }

        .email-tagline {
            color: #e2e8f0;
            font-size: 14px;
            margin: 8px 0 0 0;
        }

        .email-body {
            padding: 40px 30px;
        }

        .email-greeting {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 20px 0;
        }

        .email-content {
            font-size: 16px;
            line-height: 1.6;
            color: #374151;
            margin: 0 0 30px 0;
        }

        .email-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s ease;
        }

        .email-button:hover {
            transform: translateY(-1px);
        }

        .email-link {
            color: #667eea;
            text-decoration: none;
            word-break: break-all;
            font-size: 14px;
            background-color: #f1f5f9;
            padding: 12px;
            border-radius: 6px;
            display: block;
            margin: 15px 0;
        }

        .email-footer {
            background-color: #f8fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .email-footer-text {
            font-size: 14px;
            color: #6b7280;
            margin: 0 0 10px 0;
        }

        .email-footer-links {
            font-size: 12px;
            color: #9ca3af;
        }

        .email-footer-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }

        .security-notice {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            font-size: 14px;
            color: #92400e;
        }

        /* Responsive styles */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            .email-header, .email-body, .email-footer {
                padding: 20px !important;
            }
            .email-button {
                display: block !important;
                width: 100% !important;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1 class="email-logo">Collaboration Hub</h1>
            <p class="email-tagline">Streamline your content collaborations</p>
        </div>

        <!-- Body -->
        <div class="email-body" th:fragment="email-template">
            <div th:insert="${contentFragment}"></div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p class="email-footer-text">
                This email was sent by Collaboration Hub. If you have any questions, please contact our support team.
            </p>
            <div class="email-footer-links">
                <a href="#" th:href="${frontendUrl}">Visit Website</a>
                <a href="#" th:href="${frontendUrl + '/support'}">Support</a>
                <a href="#" th:href="${frontendUrl + '/privacy'}">Privacy Policy</a>
            </div>
        </div>
    </div>
</body>
</html>
