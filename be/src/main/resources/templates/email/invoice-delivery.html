<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'Invoice ' + ${invoice.invoiceNumber}">Invoice</title>
    <style th:replace="email/base :: email-styles"></style>
    <style>
        /* Invoice-specific styles */
        .invoice-summary {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .invoice-number {
            font-size: 18px;
            font-weight: bold;
            color: #1a202c;
        }
        
        .invoice-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-draft { background-color: #f7fafc; color: #4a5568; }
        .status-sent { background-color: #ebf8ff; color: #2b6cb0; }
        .status-overdue { background-color: #fed7d7; color: #c53030; }
        .status-paid { background-color: #c6f6d5; color: #2f855a; }
        
        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .detail-label {
            font-weight: 600;
            color: #4a5568;
        }
        
        .detail-value {
            color: #1a202c;
        }
        
        .total-amount {
            background-color: #2563eb;
            color: white;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            margin: 20px 0;
        }
        
        .total-amount .amount {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }
        
        .total-amount .label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .overdue-notice {
            background-color: #fed7d7;
            border: 1px solid #fc8181;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .overdue-notice .title {
            font-weight: bold;
            color: #c53030;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .overdue-notice .message {
            color: #742a2a;
        }
        
        .copy-notice {
            background-color: #edf2f7;
            border: 1px solid #cbd5e0;
            border-radius: 6px;
            padding: 12px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #4a5568;
        }
        
        .attachment-notice {
            background-color: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            display: flex;
            align-items: center;
        }
        
        .attachment-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            color: #38a169;
        }
        
        .payment-info {
            background-color: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .payment-info h4 {
            margin: 0 0 10px 0;
            color: #2d3748;
        }
        
        .bank-details {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            color: #4a5568;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1 class="email-logo">Collaboration Hub</h1>
            <p class="email-tagline">Professional Invoice Management</p>
        </div>

        <!-- Copy Notice -->
        <div class="copy-notice" th:if="${isCopy}">
            📋 This is a copy of the invoice for your records
        </div>

        <!-- Body -->
        <div class="email-body">
            <h2 class="email-greeting" th:text="${isCopy ? 'Invoice Copy Attached' : 'New Invoice Available'}">New Invoice Available</h2>
            
            <div class="email-content">
                <p th:text="${isCopy ? 'You are receiving a copy of this invoice for your records.' : 'Please find your invoice attached to this email.'}">
                    Please find your invoice attached to this email.
                </p>
            </div>

            <!-- Overdue Notice -->
            <div class="overdue-notice" th:if="${isOverdue}">
                <div class="title">⚠️ PAYMENT OVERDUE</div>
                <div class="message" th:text="'This invoice is overdue by ' + ${-daysUntilDue} + ' days. Please arrange payment immediately.'">
                    This invoice is overdue. Please arrange payment immediately.
                </div>
            </div>

            <!-- Invoice Summary -->
            <div class="invoice-summary">
                <div class="invoice-header">
                    <div class="invoice-number" th:text="'Invoice ' + ${invoice.invoiceNumber}">Invoice #INV-001</div>
                    <div class="invoice-status" th:class="'status-' + ${invoice.status}" th:text="${invoice.status}">sent</div>
                </div>
                
                <div class="invoice-details">
                    <div class="detail-item">
                        <span class="detail-label">Issue Date:</span>
                        <span class="detail-value" th:text="${#temporals.format(invoice.issueDate, 'dd/MM/yyyy')}">01/01/2024</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Due Date:</span>
                        <span class="detail-value" th:text="${#temporals.format(invoice.dueDate, 'dd/MM/yyyy')}">31/01/2024</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">From:</span>
                        <span class="detail-value" th:text="${issuer.name}">Company Name</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">To:</span>
                        <span class="detail-value" th:text="${recipient.name}">Client Name</span>
                    </div>
                </div>
                
                <div class="total-amount">
                    <span class="label">Total Amount Due</span>
                    <span class="amount" th:text="${formattedTotalAmount}">€121.00</span>
                </div>
            </div>

            <!-- Attachment Notice -->
            <div class="attachment-notice">
                <svg class="attachment-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <strong>PDF Invoice Attached</strong><br>
                    <span th:text="'Invoice_' + ${invoice.invoiceNumber} + '.pdf'">Invoice_INV-001.pdf</span>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="payment-info" th:if="${!isCopy}">
                <h4>💳 Payment Information</h4>
                <div class="bank-details">
                    <strong>Account Name:</strong> <span th:text="${bankDetails.accountName}">Account Name</span><br>
                    <strong>Bank:</strong> <span th:text="${bankDetails.bankName}">Bank Name</span><br>
                    <strong>IBAN:</strong> <span th:text="${bankDetails.iban}">IBAN</span><br>
                    <strong>BIC/SWIFT:</strong> <span th:text="${bankDetails.bic}">BIC</span><br>
                    <strong>Reference:</strong> <span th:text="${invoice.invoiceNumber}">INV-001</span>
                </div>
            </div>

            <!-- Notes -->
            <div class="email-content" th:if="${invoice.notes}">
                <h4>📝 Additional Notes:</h4>
                <p th:text="${invoice.notes}">Additional notes or payment terms...</p>
            </div>

            <!-- Due Date Reminder -->
            <div class="email-content" th:if="${!isOverdue and daysUntilDue != null and daysUntilDue <= 7}">
                <div style="background-color: #fef3cd; border: 1px solid #fbbf24; border-radius: 6px; padding: 15px; margin: 20px 0;">
                    <strong>⏰ Payment Reminder:</strong> This invoice is due in 
                    <span th:text="${daysUntilDue}">X</span> day<span th:if="${daysUntilDue != 1}">s</span>.
                </div>
            </div>

            <div class="email-content">
                <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>
                
                <p style="margin-top: 30px;">
                    Best regards,<br>
                    <strong th:text="${issuer.name}">Company Name</strong><br>
                    <span th:if="${issuer.email}" th:text="${issuer.email}"><EMAIL></span><br>
                    <span th:if="${issuer.phone}" th:text="${issuer.phone}">****** 567 8900</span>
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <p>This email was sent from Collaboration Hub's invoice management system.</p>
            <p>
                <a th:href="${frontendUrl}" style="color: #2563eb; text-decoration: none;">
                    Visit Collaboration Hub
                </a>
            </p>
        </div>
    </div>
</body>
</html>
