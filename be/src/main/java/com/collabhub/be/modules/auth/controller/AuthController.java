package com.collabhub.be.modules.auth.controller;

import com.collabhub.be.modules.auth.config.AuthProperties;
import com.collabhub.be.modules.auth.dto.AuthenticationResponse;
import com.collabhub.be.modules.auth.dto.EmailVerificationRequest;
import com.collabhub.be.modules.auth.dto.EmailVerificationResponse;
import com.collabhub.be.modules.auth.dto.LoginRequest;
import com.collabhub.be.modules.auth.dto.RegistrationRequest;
import com.collabhub.be.modules.auth.dto.RegistrationResponse;
import com.collabhub.be.modules.auth.model.Role;
import com.collabhub.be.modules.auth.service.AuthenticationService;
import com.collabhub.be.modules.auth.service.AuthenticationService.AuthenticationResult;
import com.collabhub.be.modules.auth.service.RegistrationService;
import com.collabhub.be.modules.auth.util.CookieUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.jooq.generated.tables.pojos.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for authentication operations.
 * Handles login, token refresh, and logout endpoints.
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    private final AuthenticationService authenticationService;
    private final RegistrationService registrationService;
    private final AuthProperties authProperties;
    private final CookieUtil cookieUtil;

    public AuthController(AuthenticationService authenticationService,
                         RegistrationService registrationService,
                         AuthProperties authProperties,
                         CookieUtil cookieUtil) {
        this.authenticationService = authenticationService;
        this.registrationService = registrationService;
        this.authProperties = authProperties;
        this.cookieUtil = cookieUtil;
    }

    /**
     * Authenticates a user and returns access token. Refresh token is set as HTTP-only cookie.
     *
     * @param loginRequest the login credentials
     * @param request the HTTP request for extracting client information
     * @param response the HTTP response for setting cookies
     * @return authentication response with access token only
     */
    @PostMapping("/login")
    public ResponseEntity<AuthenticationResponse> login(
            @Valid @RequestBody LoginRequest loginRequest,
            HttpServletRequest request,
            HttpServletResponse response) {

        logger.debug("Login attempt for email: {}", loginRequest.getEmail());

        String userAgent = request.getHeader("User-Agent");

        AuthenticationResult result = authenticationService.authenticate(
                loginRequest.getEmail(),
                loginRequest.getPassword(),
                userAgent
        );

        // Set refresh token as HTTP-only cookie
        cookieUtil.addRefreshTokenCookie(response, result.refreshToken());

        AuthenticationResponse authResponse = createAuthenticationResponse(result);

        logger.info("Successful login for user {}",
                   result.user().getId());

        return ResponseEntity.ok(authResponse);
    }

    /**
     * Refreshes an access token using a refresh token from HTTP-only cookie.
     *
     * @param request the HTTP request for extracting client information and refresh token cookie
     * @param response the HTTP response for setting new refresh token cookie
     * @return new authentication response with new access token
     */
    @PostMapping("/refresh")
    public ResponseEntity<AuthenticationResponse> refresh(
            HttpServletRequest request,
            HttpServletResponse response) {

        logger.debug("Token refresh attempt");

        // Extract refresh token from cookie
        String refreshToken = cookieUtil.getRefreshTokenFromCookies(request)
                .orElseThrow(() -> new IllegalArgumentException("Refresh token cookie not found"));

        String userAgent = request.getHeader("User-Agent");

        AuthenticationResult result = authenticationService.refreshToken(
                refreshToken,
                userAgent
        );

        // Set new refresh token as HTTP-only cookie (token rotation)
        cookieUtil.addRefreshTokenCookie(response, result.refreshToken());

        AuthenticationResponse authResponse = createAuthenticationResponse(result);

        logger.info("Successful token refresh for user {}",
                   result.user().getId());

        return ResponseEntity.ok(authResponse);
    }

    /**
     * Logs out a user by revoking their refresh token and clearing the cookie.
     *
     * @param request the HTTP request for extracting refresh token cookie
     * @param response the HTTP response for clearing the refresh token cookie
     * @return success response
     */
    @PostMapping("/logout")
    public ResponseEntity<Void> logout(
            HttpServletRequest request,
            HttpServletResponse response) {

        logger.debug("Logout attempt");

        // Extract refresh token from cookie if present
        cookieUtil.getRefreshTokenFromCookies(request)
                .ifPresent(authenticationService::logout);

        // Clear the refresh token cookie
        cookieUtil.clearRefreshTokenCookie(response);

        logger.info("Successful logout");

        return ResponseEntity.ok().build();
    }

    /**
     * Registers a new user and creates a new account.
     * The registering user becomes the admin of the newly created account.
     *
     * @param registrationRequest the registration data
     * @return registration response with verification details
     */
    @PostMapping("/register")
    public ResponseEntity<RegistrationResponse> register(
            @Valid @RequestBody RegistrationRequest registrationRequest) {

        logger.debug("Registration attempt for email: {}", registrationRequest.getEmail());

        RegistrationResponse response = registrationService.registerUser(registrationRequest);

        logger.info("Registration completed for email: {}", registrationRequest.getEmail());

        return ResponseEntity.ok(response);
    }

    /**
     * Verifies a user's email address using the verification token.
     * Enables the user account upon successful verification.
     *
     * @param verificationRequest the email verification request containing the token
     * @return verification response with confirmation details
     */
    @PostMapping("/verify-email")
    public ResponseEntity<EmailVerificationResponse> verifyEmail(
            @Valid @RequestBody EmailVerificationRequest verificationRequest) {

        logger.debug("Email verification attempt for token: {}",
                    maskToken(verificationRequest.getToken()));

        EmailVerificationResponse response = registrationService.verifyEmail(verificationRequest);

        logger.info("Email verification completed for email: {}", response.getEmail());

        return ResponseEntity.ok(response);
    }

    /**
     * Health check endpoint for the authentication service.
     *
     * @return health status
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Authentication service is healthy");
    }

    /**
     * Creates an authentication response without refresh token from the authentication result.
     *
     * @param result the authentication result
     * @return the response DTO without refresh token
     */
    private AuthenticationResponse createAuthenticationResponse(AuthenticationResult result) {
        User user = result.user();
        Role role = Role.fromString(user.getRole());

        AuthenticationResponse.UserInfo userInfo = new AuthenticationResponse.UserInfo(
                user.getId(),
                user.getEmail(),
                user.getDisplayName(),
                role,
                role.getPermissions(),
                user.getAccountId(),
                user.getInternal()
        );

        long expiresInSeconds = authProperties.getJwt().getAccessTokenTtl().toSeconds();

        return new AuthenticationResponse(
                result.accessToken(),
                expiresInSeconds,
                userInfo
        );
    }

    /**
     * Masks a token for logging purposes to prevent token exposure.
     *
     * @param token the token to mask
     * @return masked token showing only first and last 4 characters
     */
    private String maskToken(String token) {
        if (token == null || token.length() < 8) {
            return "****";
        }
        return token.substring(0, 4) + "****" + token.substring(token.length() - 4);
    }
}
