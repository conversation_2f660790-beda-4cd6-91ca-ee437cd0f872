package com.collabhub.be.modules.chat.service;

import com.collabhub.be.modules.chat.dto.*;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.chat.converter.ChatConverter;
import com.collabhub.be.modules.chat.repository.ChatChannelRepositoryImpl;
import com.collabhub.be.modules.chat.repository.ChatChannelParticipantRepositoryImpl;
import com.collabhub.be.modules.chat.repository.ChatMessageRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.ChatChannel;
import org.jooq.generated.tables.pojos.ChatMessage;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing chat channels and their access control.
 * Handles channel creation, access validation, and participant management.
 */
@Service
@Validated
public class ChatChannelService {

    private static final Logger logger = LoggerFactory.getLogger(ChatChannelService.class);

    // Constants
    private static final int DEFAULT_CHANNEL_PAGE_SIZE = 20;
    private static final String PARTICIPANT_NOT_FOUND_MESSAGE = "Participant not found in hub";
    private static final String CHANNEL_ACCESS_DENIED_MESSAGE = "Access denied to chat channel";
    private static final String PARTICIPANT_NOT_FOUND_GENERAL_MESSAGE = "Participant not found";
    private static final String PARTICIPANT_NOT_FOUND_FOR_USER_MESSAGE = "Participant not found for user in hub %d";

    private final ChatChannelRepositoryImpl channelRepository;
    private final ChatChannelParticipantRepositoryImpl channelParticipantRepository;
    private final ChatMessageRepositoryImpl messageRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final ChatConverter chatConverter;

    public ChatChannelService(ChatChannelRepositoryImpl channelRepository,
                            ChatChannelParticipantRepositoryImpl channelParticipantRepository,
                            ChatMessageRepositoryImpl messageRepository,
                            HubParticipantRepositoryImpl participantRepository,
                            ChatConverter chatConverter) {
        this.channelRepository = channelRepository;
        this.channelParticipantRepository = channelParticipantRepository;
        this.messageRepository = messageRepository;
        this.participantRepository = participantRepository;
        this.chatConverter = chatConverter;
    }

    /**
     * Gets all accessible chat channels for a hub participant with pagination.
     *
     * @param hubId the hub ID
     * @param participantId the participant ID
     * @param pageRequest the pagination request
     * @return paginated list of accessible channels
     */
    @Transactional(readOnly = true)
    public PageResponse<ChatChannelResponse> getAccessibleChannels(@NotNull Long hubId,
                                                                  @NotNull Long participantId,
                                                                  @Valid PageRequest pageRequest) {
        logger.info("Getting accessible channels for participant {} in hub {} (page: {}, size: {})",
                   participantId, hubId, pageRequest.getPage(), pageRequest.getSize());

        HubParticipant participant = validateAndGetParticipant(participantId, hubId);
        List<ChatChannel> channels = fetchAccessibleChannels(hubId, participantId, participant.getRole(), pageRequest);
        long totalChannels = countAccessibleChannels(hubId, participantId, participant.getRole());
        List<ChatChannelResponse> channelResponses = convertChannelsToResponses(channels, participantId, participant.getRole());

        logChannelRetrievalResults(channelResponses, pageRequest, totalChannels, participantId, hubId);
        return new PageResponse<>(channelResponses, pageRequest, totalChannels);
    }

    /**
     * Validates and retrieves participant by ID and hub ID.
     */
    private HubParticipant validateAndGetParticipant(Long participantId, Long hubId) {
        return participantRepository.findByIdAndHubId(participantId, hubId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND,
                        PARTICIPANT_NOT_FOUND_MESSAGE));
    }

    /**
     * Fetches accessible channels for participant with pagination.
     */
    private List<ChatChannel> fetchAccessibleChannels(Long hubId, Long participantId,
                                                     HubParticipantRole role, PageRequest pageRequest) {
        return channelRepository.findAccessibleChannelsByHubAndParticipantWithPagination(
                hubId, participantId, role, pageRequest);
    }

    /**
     * Counts total accessible channels for participant.
     */
    private long countAccessibleChannels(Long hubId, Long participantId, HubParticipantRole role) {
        return channelRepository.countAccessibleChannelsByHubAndParticipant(hubId, participantId, role);
    }

    /**
     * Converts channels to response DTOs with bulk loading optimization.
     */
    private List<ChatChannelResponse> convertChannelsToResponses(List<ChatChannel> channels,
                                                               Long participantId, HubParticipantRole role) {
        return channels.stream()
                .map(channel -> convertToChannelResponse(channel, participantId, role))
                .toList();
    }

    /**
     * Logs channel retrieval results.
     */
    private void logChannelRetrievalResults(List<ChatChannelResponse> responses, PageRequest pageRequest,
                                          long totalChannels, Long participantId, Long hubId) {
        logger.info("Found {} accessible channels (page {}/{}) for participant {} in hub {}",
                responses.size(), pageRequest.getPage() + 1,
                (int) Math.ceil((double) totalChannels / pageRequest.getSize()), participantId, hubId);
    }

    /**
     * Gets a specific chat channel if the participant has access to it.
     *
     * @param channelId the channel ID
     * @param participantId the participant ID
     * @return channel response
     */
    @Transactional(readOnly = true)
    public ChatChannelResponse getAccessibleChannel(@NotNull Long channelId, @NotNull Long participantId) {
        logger.info("Getting channel {} for participant {}", channelId, participantId);

        HubParticipant participant = validateParticipantExists(participantId);
        ChatChannel channel = validateChannelAccess(channelId, participantId);

        return convertToChannelResponse(channel, participantId, participant.getRole());
    }

    /**
     * Validates that participant exists.
     */
    private HubParticipant validateParticipantExists(Long participantId) {
        return participantRepository.findOptionalById(participantId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND,
                        PARTICIPANT_NOT_FOUND_GENERAL_MESSAGE));
    }

    /**
     * Validates channel access and returns the channel.
     */
    private ChatChannel validateChannelAccess(Long channelId, Long participantId) {
        return channelRepository.findAccessibleChannelById(channelId, participantId)
                .orElseThrow(() -> new ForbiddenException(ErrorCode.ACCESS_DENIED,
                        CHANNEL_ACCESS_DENIED_MESSAGE));
    }

    /**
     * Creates a general chat channel when a new hub is created.
     * This replaces the old createDefaultChannelsForHub method.
     *
     * @param hubId the hub ID
     * @param createdByParticipantId the participant who created the hub (admin)
     * @return the created general channel
     */
    @Transactional
    public ChatChannel createGeneralChannelForHub(@NotNull Long hubId, @NotNull Long createdByParticipantId) {
        logger.info("Creating general chat channel for hub {} by participant {}", hubId, createdByParticipantId);

        ChatChannel channel = channelRepository.createGeneralChannel(hubId, createdByParticipantId);
        logger.info("Successfully created general channel {} for hub {}", channel.getId(), hubId);

        return channel;
    }







    /**
     * Creates a custom chat channel with selected participants.
     *
     * @param hubId the hub ID
     * @param request the channel creation request
     * @param creatorParticipantId the participant creating the channel
     * @return the created channel response
     */
    @Transactional
    public ChatChannelResponse createCustomChannel(@NotNull Long hubId,
                                                  @Valid ChatChannelCreateRequest request,
                                                  @NotNull Long creatorParticipantId) {
        logger.info("Creating custom channel '{}' in hub {} by participant {}",
                   request.getName(), hubId, creatorParticipantId);

        // Validate that creator is a participant in the hub
        HubParticipant creator = validateAndGetParticipant(creatorParticipantId, hubId);

        // Validate that all selected participants exist in the hub
        validateParticipantsExistInHub(request.getParticipantIds(), hubId);

        // Create the custom channel
        ChatChannel channel = channelRepository.createCustomChannel(
                hubId, request.getName(), request.getDescription(), creatorParticipantId);

        // Add participants to the channel (including the creator)
        List<Long> allParticipantIds = new ArrayList<>(request.getParticipantIds());
        if (!allParticipantIds.contains(creatorParticipantId)) {
            allParticipantIds.add(creatorParticipantId);
        }
        channelParticipantRepository.addParticipantsToChannel(channel.getId(), allParticipantIds, creatorParticipantId);

        logger.info("Successfully created custom channel {} with {} participants",
                   channel.getId(), allParticipantIds.size());

        return convertToChannelResponse(channel, creatorParticipantId, creator.getRole());
    }

    /**
     * Adds participants to a custom chat channel.
     *
     * @param hubId the hub ID
     * @param channelId the channel ID
     * @param request the participant request
     * @param requestingParticipantId the participant making the request
     */
    @Transactional
    public void addParticipantsToChannel(@NotNull Long hubId, @NotNull Long channelId,
                                       @Valid ChatChannelParticipantRequest request,
                                       @NotNull Long requestingParticipantId) {
        logger.info("Adding {} participants to channel {} by participant {}",
                   request.getParticipantIds().size(), channelId, requestingParticipantId);

        // Validate channel exists and is custom
        ChatChannel channel = validateCustomChannelAccess(channelId, requestingParticipantId);

        // Validate that all participants exist in the hub
        validateParticipantsExistInHub(request.getParticipantIds(), hubId);

        // Add participants to the channel
        channelParticipantRepository.addParticipantsToChannel(
                channelId, request.getParticipantIds(), requestingParticipantId);

        logger.info("Successfully added {} participants to channel {}",
                   request.getParticipantIds().size(), channelId);
    }

    /**
     * Removes participants from a custom chat channel.
     *
     * @param hubId the hub ID
     * @param channelId the channel ID
     * @param request the participant request
     * @param requestingParticipantId the participant making the request
     */
    @Transactional
    public void removeParticipantsFromChannel(@NotNull Long hubId, @NotNull Long channelId,
                                            @Valid ChatChannelParticipantRequest request,
                                            @NotNull Long requestingParticipantId) {
        logger.info("Removing {} participants from channel {} by participant {}",
                   request.getParticipantIds().size(), channelId, requestingParticipantId);

        // Validate channel exists and is custom
        validateCustomChannelAccess(channelId, requestingParticipantId);

        // Remove participants from the channel
        channelParticipantRepository.removeParticipantsFromChannel(channelId, request.getParticipantIds());

        logger.info("Successfully removed {} participants from channel {}",
                   request.getParticipantIds().size(), channelId);
    }

    /**
     * Deletes a custom chat channel.
     *
     * @param channelId the channel ID
     * @param requestingParticipantId the participant making the request
     */
    @Transactional
    public void deleteCustomChannel(@NotNull Long channelId, @NotNull Long requestingParticipantId) {
        logger.info("Deleting custom channel {} by participant {}", channelId, requestingParticipantId);

        // Validate channel exists and is custom
        ChatChannel channel = validateCustomChannelAccess(channelId, requestingParticipantId);

        // Remove all participants first
        channelParticipantRepository.removeAllParticipantsFromChannel(channelId);

        // Delete the channel
        channelRepository.deleteById(channelId);

        logger.info("Successfully deleted custom channel {}", channelId);
    }

    /**
     * Updates the last activity timestamp for a channel.
     * Called when messages are sent to keep channels sorted by activity.
     *
     * @param channelId the channel ID
     */
    @Transactional
    public void updateChannelActivity(@NotNull Long channelId) {
        channelRepository.updateLastActivity(channelId);
        logger.debug("Updated last activity for channel {}", channelId);
    }

    /**
     * Checks if a participant has write access to a channel.
     *
     * @param channelId the channel ID
     * @param participantId the participant ID
     * @return true if participant can write to channel
     */
    @Transactional(readOnly = true)
    public boolean canParticipantWriteToChannel(@NotNull Long channelId, @NotNull Long participantId) {
        HubParticipant participant = participantRepository.findById(participantId);

        return participant != null && hasChannelAccess(channelId, participantId);
    }

    /**
     * Checks if participant has access to channel.
     */
    private boolean hasChannelAccess(Long channelId, Long participantId) {
        Optional<ChatChannel> channel = channelRepository.findAccessibleChannelById(channelId, participantId);
        return channel.isPresent();
    }

    /**
     * Gets all accessible chat channels for a hub by user context with pagination.
     * Handles participant resolution internally.
     *
     * @param hubId the hub ID
     * @param userContext the user context
     * @param pageRequest the pagination request
     * @return paginated list of accessible channels
     */
    @Transactional(readOnly = true)
    public PageResponse<ChatChannelResponse> getAccessibleChannelsByUser(@NotNull Long hubId,
                                                                        @NotNull UserContext userContext,
                                                                        @Valid PageRequest pageRequest) {
        Long participantId = resolveParticipantId(hubId, userContext);
        return getAccessibleChannels(hubId, participantId, pageRequest);
    }

    /**
     * Gets a specific chat channel by user context if the user has access to it.
     * Handles participant resolution internally.
     *
     * @param hubId the hub ID
     * @param channelId the channel ID
     * @param userContext the user context
     * @return channel response
     */
    @Transactional(readOnly = true)
    public ChatChannelResponse getAccessibleChannelByUser(@NotNull Long hubId,
                                                         @NotNull Long channelId,
                                                         @NotNull UserContext userContext) {
        Long participantId = resolveParticipantId(hubId, userContext);
        return getAccessibleChannel(channelId, participantId);
    }

    /**
     * Resolves participant ID from user context.
     * Handles both internal users and external participants.
     */
    private Long resolveParticipantId(Long hubId, UserContext userContext) {
        // Try internal user lookup first
        Long participantId = resolveInternalParticipant(hubId, userContext);
        if (participantId != null) {
            return participantId;
        }

        // Fallback to external participant lookup
        return resolveExternalParticipant(hubId, userContext);
    }

    /**
     * Resolves internal participant by user ID.
     */
    private Long resolveInternalParticipant(Long hubId, UserContext userContext) {
        if (userContext.getUserId() != null) {
            Optional<HubParticipant> participant = participantRepository
                    .findByUserIdAndHubId(userContext.getUserId(), hubId);
            return participant.map(HubParticipant::getId).orElse(null);
        }
        return null;
    }

    /**
     * Resolves external participant by email.
     */
    private Long resolveExternalParticipant(Long hubId, UserContext userContext) {
        Optional<HubParticipant> participant = participantRepository
                .findByEmailAndHubId(userContext.getEmail(), hubId);

        return participant.map(HubParticipant::getId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND,
                        String.format(PARTICIPANT_NOT_FOUND_FOR_USER_MESSAGE, hubId)));
    }

    /**
     * Validates that all participant IDs exist in the specified hub.
     */
    private void validateParticipantsExistInHub(List<Long> participantIds, Long hubId) {
        for (Long participantId : participantIds) {
            HubParticipant participant = participantRepository.findById(participantId);
            if (participant == null || !participant.getHubId().equals(hubId) || participant.getRemovedAt() != null) {
                throw new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND,
                        "Participant " + participantId + " not found in hub " + hubId);
            }
        }
    }

    /**
     * Gets participant ID by user context (email and hub).
     * Public method for controller access.
     */
    public Long getParticipantIdByUserContext(Long hubId, UserContext userContext) {
        Optional<HubParticipant> participant = participantRepository
                .findByEmailAndHubId(userContext.getEmail(), hubId);

        return participant.map(HubParticipant::getId)
                .orElseThrow(() -> new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND,
                        String.format(PARTICIPANT_NOT_FOUND_FOR_USER_MESSAGE, hubId)));
    }

    /**
     * Validates that a channel is custom and the requesting participant can manage it.
     * Only channel creators and hub admins can manage custom channels.
     */
    private ChatChannel validateCustomChannelAccess(Long channelId, Long requestingParticipantId) {
        ChatChannel channel = channelRepository.fetchOneById(channelId);
        if (channel == null) {
            throw new NotFoundException(ErrorCode.CHAT_CHANNEL_NOT_FOUND, "Channel not found");
        }

        // Only custom channels can be managed
        if (!channel.getScope().name().equals("custom")) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED, "Only custom channels can be managed");
        }

        // Check if requesting participant is the creator or a hub admin
        HubParticipant requestingParticipant = participantRepository.findById(requestingParticipantId);
        if (requestingParticipant == null) {
            throw new NotFoundException(ErrorCode.HUB_PARTICIPANT_NOT_FOUND, "Participant not found");
        }

        boolean isCreator = channel.getCreatedByParticipantId().equals(requestingParticipantId);
        boolean isAdmin = requestingParticipant.getRole() == HubParticipantRole.admin;

        if (!isCreator && !isAdmin) {
            throw new ForbiddenException(ErrorCode.ACCESS_DENIED,
                    "Only channel creators and hub admins can manage custom channels");
        }

        return channel;
    }

    /**
     * Converts a ChatChannel entity to response DTO with additional metadata.
     */
    private ChatChannelResponse convertToChannelResponse(ChatChannel channel, Long participantId,
                                                       HubParticipantRole participantRole) {
        Long participantCount = getChannelParticipantCount(channel.getId());
        Long unreadCount = getUnreadMessageCount(channel.getId(), participantId);
        ChatMessageResponse lastMessage = getLastMessageResponse(channel.getId());

        // Get participants for custom channels
        List<ChatParticipantDto> participants = null;
        if (channel.getScope().name().equals("custom")) {
            participants = getChannelParticipants(channel.getId());
        }

        // Determine permissions
        boolean canAccess = true; // Already validated by access control
        boolean canWrite = true;  // Simplified for now
        boolean canManage = canManageChannel(channel, participantId, participantRole);

        return chatConverter.toChannelResponse(channel, participants, participantCount, unreadCount,
                lastMessage, canAccess, canWrite, canManage);
    }

    /**
     * Gets participants for a custom channel.
     */
    private List<ChatParticipantDto> getChannelParticipants(Long channelId) {
        List<Long> participantIds = channelParticipantRepository.getChannelParticipantIds(channelId);
        return participantIds.stream()
                .map(participantRepository::findById)
                .filter(participant -> participant != null)
                .map(chatConverter::toParticipantDto)
                .toList();
    }

    /**
     * Checks if a participant can manage a channel.
     */
    private boolean canManageChannel(ChatChannel channel, Long participantId, HubParticipantRole participantRole) {
        // Only custom channels can be managed
        if (!channel.getScope().name().equals("custom")) {
            return false;
        }

        // Channel creator or hub admin can manage
        return channel.getCreatedByParticipantId().equals(participantId) ||
               participantRole == HubParticipantRole.admin;
    }

    /**
     * Gets participant count for channel.
     */
    private Long getChannelParticipantCount(Long channelId) {
        return channelRepository.countChannelParticipants(channelId);
    }

    /**
     * Gets unread message count for participant in channel.
     */
    private Long getUnreadMessageCount(Long channelId, Long participantId) {
        return channelRepository.countUnreadMessages(channelId, participantId);
    }

    /**
     * Gets last message response for channel.
     */
    private ChatMessageResponse getLastMessageResponse(Long channelId) {
        Optional<ChatMessage> latestMessage = messageRepository.findLatestMessageByChannel(channelId);

        return latestMessage.map(this::convertMessageToResponse).orElse(null);
    }

    /**
     * Converts message to response with sender details.
     */
    private ChatMessageResponse convertMessageToResponse(ChatMessage message) {
        HubParticipant sender = participantRepository.findById(message.getParticipantId());
        if (sender != null) {
            // For last message preview, we don't need to load media (performance optimization)
            return chatConverter.toMessageResponse(message, List.of(), chatConverter.toParticipantDto(sender));
        }
        return null;
    }
}
