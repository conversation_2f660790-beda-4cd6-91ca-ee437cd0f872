package com.collabhub.be.modules.posts.repository;

import com.collabhub.be.modules.posts.dto.PostFilter;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.generated.enums.ReviewStatus;
import org.jooq.generated.tables.daos.PostDao;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.Post;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

import static org.jooq.generated.tables.Post.POST;
import static org.jooq.generated.tables.HubParticipant.HUB_PARTICIPANT;
import static org.jooq.generated.tables.CollaborationHub.COLLABORATION_HUB;
import static org.jooq.generated.tables.PostReviewer.POST_REVIEWER;

/**
 * Repository for Post entity using jOOQ for database operations.
 * Provides type-safe database operations with multi-tenancy support.
 */
@Repository
public class PostRepositoryImpl extends PostDao {

    private final DSLContext dsl;

    public PostRepositoryImpl(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    /**
     * Finds posts in a hub with role-based filtering and pagination.
     * Excludes soft-deleted posts.
     */
    public List<Post> findPosts(Long hubId, Long userId,
                                String filter, ReviewStatus status, int offset, int limit) {
        Condition baseCondition = buildPostFilterConditions(hubId, userId, filter, status);

        return dsl.selectFrom(POST)
                .where(baseCondition)
                .orderBy(POST.CREATED_AT.desc())
                .offset(offset)
                .limit(limit)
                .fetchInto(Post.class);
    }

    /**
     * Counts posts with role-based filtering.
     * Excludes soft-deleted posts.
     */
    public long countPosts(Long hubId, Long userId,
                           String filter, ReviewStatus status) {
        Condition baseCondition = buildPostFilterConditions(hubId, userId, filter, status);

        return dsl.selectCount()
                .from(POST)
                .where(baseCondition)
                .fetchOne(0, Long.class);
    }

    /**
     * Finds a post by ID with hub access validation.
     * Excludes soft-deleted posts.
     */
    public Optional<Post> findByIdWithHubAccess(Long postId, Long hubId, Long accountId) {
        return dsl.select(POST.fields())
                .from(POST)
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(POST.HUB_ID))
                .where(POST.ID.eq(postId))
                .and(POST.HUB_ID.eq(hubId))
                .and(POST.DELETED_AT.isNull()) // Exclude soft-deleted posts
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .fetchOptionalInto(Post.class);
    }

    /**
     * Finds posts by hub ID with account validation.
     * Excludes soft-deleted posts.
     */
    public List<Post> findByHubIdWithAccountValidation(Long hubId, Long accountId) {
        return dsl.select(POST.fields())
                .from(POST)
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(POST.HUB_ID))
                .where(POST.HUB_ID.eq(hubId))
                .and(POST.DELETED_AT.isNull()) // Exclude soft-deleted posts
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .orderBy(POST.CREATED_AT.desc())
                .fetchInto(Post.class);
    }

    /**
     * Finds a post by ID with account validation.
     * Excludes soft-deleted posts.
     */
    public Optional<Post> findByIdWithAccountValidation(Long postId, Long accountId) {
        return dsl.select(POST.fields())
                .from(POST)
                .join(COLLABORATION_HUB).on(COLLABORATION_HUB.ID.eq(POST.HUB_ID))
                .where(POST.ID.eq(postId))
                .and(POST.DELETED_AT.isNull()) // Exclude soft-deleted posts
                .and(COLLABORATION_HUB.ACCOUNT_ID.eq(accountId))
                .fetchOptionalInto(Post.class);
    }

    /**
     * Gets the participant ID for a user in a specific hub.
     */
    public HubParticipant getUserParticipant(Long hubId, Long userId) {
        return dsl.select(HUB_PARTICIPANT)
                .from(HUB_PARTICIPANT)
                .where(HUB_PARTICIPANT.HUB_ID.eq(hubId))
                .and(HUB_PARTICIPANT.USER_ID.eq(userId))
                .and(HUB_PARTICIPANT.REMOVED_AT.isNull())
                .fetchOneInto(HubParticipant.class);
    }

    /**
     * Updates a post's review status.
     */
    public boolean updateReviewStatus(Long postId, ReviewStatus status) {
        int updated = dsl.update(POST)
                .set(POST.REVIEW_STATUS, status)
                .set(POST.UPDATED_AT, org.jooq.impl.DSL.currentLocalDateTime())
                .where(POST.ID.eq(postId))
                .and(POST.DELETED_AT.isNull()) // Only update non-deleted posts
                .execute();

        return updated > 0;
    }

    /**
     * Soft deletes a post by setting deleted_at timestamp.
     *
     * @param postId the post ID
     * @return true if the post was soft deleted, false if not found
     */
    public boolean softDeletePost(Long postId) {
        int updated = dsl.update(POST)
                .set(POST.DELETED_AT, org.jooq.impl.DSL.currentLocalDateTime())
                .set(POST.UPDATED_AT, org.jooq.impl.DSL.currentLocalDateTime())
                .where(POST.ID.eq(postId))
                .and(POST.DELETED_AT.isNull()) // Only delete non-deleted posts
                .execute();

        return updated > 0;
    }

    /**
     * Finds a post by ID excluding soft-deleted posts.
     */
    @Override
    public Post findById(Long id) {
        return dsl.selectFrom(POST)
                .where(POST.ID.eq(id))
                .and(POST.DELETED_AT.isNull())
                .fetchOneInto(Post.class);
    }

    /**
     * Builds common filter conditions for post queries.
     * Centralizes filtering logic to avoid duplication between find and count methods.
     */
    private Condition buildPostFilterConditions(Long hubId, Long userId,
                                               String filter, ReviewStatus status) {
        Condition baseCondition = POST.HUB_ID.eq(hubId).and(POST.DELETED_AT.isNull());

        // Apply additional filters
        HubParticipant participant = getUserParticipant(hubId, userId);

        if (PostFilter.ASSIGNED_TO_ME.getValue().equals(filter)) {
            if (participant != null) {
                baseCondition = baseCondition.and(POST.ID.in(
                        dsl.select(POST_REVIEWER.POST_ID)
                                .from(POST_REVIEWER)
                                .where(POST_REVIEWER.PARTICIPANT_ID.eq(participant.getId()))
                ));
            }
        } else if (PostFilter.CREATED_BY_ME.getValue().equals(filter)) {
            if (participant != null) {
                baseCondition = baseCondition.and(POST.CREATOR_PARTICIPANT_ID.eq(participant.getId()));
            }
        } else if (PostFilter.NEEDS_REVIEW.getValue().equals(filter)) {
            baseCondition = baseCondition.and(POST.REVIEW_STATUS.eq(ReviewStatus.pending));
        } else if (PostFilter.MY_PENDING.getValue().equals(filter)) {
            if (participant != null) {
                baseCondition = baseCondition.and(POST.CREATOR_PARTICIPANT_ID.eq(participant.getId()))
                        .and(POST.REVIEW_STATUS.eq(ReviewStatus.pending));
            }
        } else if (PostFilter.MY_APPROVED.getValue().equals(filter)) {
            if (participant != null) {
                baseCondition = baseCondition.and(POST.CREATOR_PARTICIPANT_ID.eq(participant.getId()))
                        .and(POST.REVIEW_STATUS.eq(ReviewStatus.approved));
            }
        } else if (PostFilter.MY_REWORK.getValue().equals(filter)) {
            if (participant != null) {
                baseCondition = baseCondition.and(POST.CREATOR_PARTICIPANT_ID.eq(participant.getId()))
                        .and(POST.REVIEW_STATUS.eq(ReviewStatus.rework));
            }
        } else if (PostFilter.REVIEWED_BY_ME.getValue().equals(filter)) {
            if (participant != null) {
                baseCondition = baseCondition.and(POST.ID.in(
                        dsl.select(POST_REVIEWER.POST_ID)
                                .from(POST_REVIEWER)
                                .where(POST_REVIEWER.PARTICIPANT_ID.eq(participant.getId()))
                                .and(POST_REVIEWER.REVIEW_STATUS.isNotNull())
                ));
            }
        }

        // Apply status filter
        if (status != null) {
            baseCondition = baseCondition.and(POST.REVIEW_STATUS.eq(status));
        }

        return baseCondition;
    }
}
