package com.collabhub.be.modules.collaborationhub.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Enum representing the scope/visibility of a collaboration brief.
 * Defines who can access and view the brief within a collaboration hub.
 */
public enum BriefScopeDto {
    
    /**
     * Brief is visible to all participants in the hub.
     */
    ALL_PARTICIPANTS("all_participants"),
    
    /**
     * Brief is visible only to admins and reviewers (including reviewer_creators).
     */
    ADMINS_REVIEWERS("admins_reviewers"),
    
    /**
     * Brief is visible only to admins.
     */
    ADMINS_ONLY("admins_only"),
    
    /**
     * Brief is visible to specific participants selected by the creator.
     * When this scope is used, specificParticipantIds should be provided.
     */
    CUSTOM_SELECTION("custom_selection");
    
    private final String value;
    
    BriefScopeDto(String value) {
        this.value = value;
    }
    
    @JsonValue
    public String getValue() {
        return value;
    }
    
    /**
     * Creates a BriefScope from a string value.
     *
     * @param value the string value
     * @return the corresponding BriefScope
     * @throws IllegalArgumentException if the value is not recognized
     */
    @JsonCreator
    public static BriefScopeDto fromValue(String value) {
        for (BriefScopeDto scope : BriefScopeDto.values()) {
            if (scope.value.equals(value)) {
                return scope;
            }
        }
        throw new IllegalArgumentException("Unknown BriefScope value: " + value);
    }
    
    @Override
    public String toString() {
        return value;
    }
}
