package com.collabhub.be.modules.chat.service;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.model.Permission;
import com.collabhub.be.modules.auth.service.EmailService;
import com.collabhub.be.modules.chat.dto.ChatMessageResponse;
import com.collabhub.be.modules.chat.dto.MentionDto;
import com.collabhub.be.modules.chat.repository.ChatChannelRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import org.jooq.DSLContext;

import static org.jooq.generated.Tables.CHAT_CHANNEL_PARTICIPANTS;
import org.jooq.generated.enums.ChatChannelScope;
import org.jooq.generated.enums.HubParticipantRole;
import org.jooq.generated.tables.pojos.ChatChannel;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.jooq.generated.Tables.CHAT_CHANNEL_PARTICIPANTS;

/**
 * Service for handling real-time chat notifications via WebSocket.
 * Manages broadcasting messages, typing indicators, and user presence.
 * Provides asynchronous email notifications for external participants.
 */
@Service
@Validated
public class ChatNotificationService {

    private static final Logger logger = LoggerFactory.getLogger(ChatNotificationService.class);

    // WebSocket destination constants
    private static final String CHANNEL_MESSAGE_TOPIC_PATTERN = "/topic/chat/channel/%d/user/%d";
    private static final String GENERAL_CHANNEL_TOPIC_PATTERN = "/topic/chat/channel/%d";
    private static final String MENTION_QUEUE_PATTERN = "/queue/mentions/user/%d";
    private static final String TYPING_TOPIC_PATTERN = "/topic/chat/channel/%d/typing";
    private static final String PRESENCE_TOPIC_PATTERN = "/topic/chat/channel/%d/presence";
    private static final String ERROR_QUEUE_PATTERN = "/queue/errors/user/%s";

    // Notification type constants
    private static final String MENTION_TYPE = "mention";
    private static final String TYPING_TYPE = "typing";
    private static final String USER_JOINED_TYPE = "user_joined";
    private static final String USER_LEFT_TYPE = "user_left";
    private static final String ERROR_TYPE = "error";

    // Email notification constants
    private static final String MENTION_EMAIL_SUBJECT = "You've been mentioned in a collaboration chat";
    private static final String MENTION_EMAIL_TEMPLATE = """
            Hello %s,

            You've been mentioned in a chat message:

            "%s"

            Please log in to your collaboration hub to view the full conversation.

            Best regards,
            Collaboration Hub Team""";
    private static final String DEFAULT_GREETING_NAME = "there";
    private static final String PARTICIPANT_NOT_FOUND_MESSAGE = "Participant not found for email: %s";

    private final SimpMessagingTemplate messagingTemplate;
    private final ChatChannelRepositoryImpl channelRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final EmailService emailService;
    private final DSLContext dsl;

    public ChatNotificationService(SimpMessagingTemplate messagingTemplate,
                                 ChatChannelRepositoryImpl channelRepository,
                                 HubParticipantRepositoryImpl participantRepository,
                                 EmailService emailService,
                                 DSLContext dsl) {
        this.messagingTemplate = messagingTemplate;
        this.channelRepository = channelRepository;
        this.participantRepository = participantRepository;
        this.emailService = emailService;
        this.dsl = dsl;
    }

    /**
     * Broadcasts a new chat message to all participants in a channel.
     *
     * @param channelId the channel ID
     * @param message the message to broadcast
     */
    @Transactional(readOnly = true)
    public void broadcastMessageToChannel(@NotNull Long channelId, @NotNull ChatMessageResponse message) {
        logger.info("Broadcasting message {} to channel {}", message.getId(), channelId);

        ChatChannel channel = getChannelById(channelId);
        if (channel == null) {
            logger.warn("Channel {} not found for message broadcast", channelId);
            return;
        }

        List<HubParticipant> participants = getChannelParticipants(channel);
        broadcastToParticipants(channelId, message, participants);
        broadcastToGeneralChannel(channelId, message);

        logBroadcastSuccess(message.getId(), participants.size(), channelId);
    }

    /**
     * Gets channel by ID.
     */
    private ChatChannel getChannelById(Long channelId) {
        return channelRepository.fetchOneById(channelId);
    }

    /**
     * Broadcasts message to individual participants.
     */
    private void broadcastToParticipants(Long channelId, ChatMessageResponse message, List<HubParticipant> participants) {
        for (HubParticipant participant : participants) {
            String destination = String.format(CHANNEL_MESSAGE_TOPIC_PATTERN, channelId, participant.getId());
            messagingTemplate.convertAndSend(destination, message);
            logger.debug("Sent message to participant {} at destination {}",
                        participant.getId(), destination);
        }
    }

    /**
     * Broadcasts message to general channel topic.
     */
    private void broadcastToGeneralChannel(Long channelId, ChatMessageResponse message) {
        String channelDestination = String.format(GENERAL_CHANNEL_TOPIC_PATTERN, channelId);
        messagingTemplate.convertAndSend(channelDestination, message);
    }

    /**
     * Logs successful broadcast.
     */
    private void logBroadcastSuccess(Long messageId, int participantCount, Long channelId) {
        logger.info("Successfully broadcasted message {} to {} participants in channel {}",
                   messageId, participantCount, channelId);
    }

    /**
     * Sends mention notifications to mentioned participants.
     *
     * @param message the message containing mentions
     */
    @Transactional(readOnly = true)
    public void sendMentionNotifications(@NotNull ChatMessageResponse message) {
        if (message.getMentions() == null || message.getMentions().isEmpty()) {
            return;
        }

        logger.info("Sending mention notifications for message {}", message.getId());

        for (MentionDto mention : message.getMentions()) {
            sendMentionNotification(mention, message);
            processEmailNotificationForExternalParticipant(mention, message);
        }

        logMentionNotificationSuccess(message);
    }

    /**
     * Sends mention notification to participant.
     */
    private void sendMentionNotification(MentionDto mention, ChatMessageResponse message) {
        String destination = String.format(MENTION_QUEUE_PATTERN, mention.getParticipantId());
        Map<String, Object> notification = createMentionNotification(mention, message);

        messagingTemplate.convertAndSend(destination, notification);
        logger.debug("Sent mention notification to participant {} for message {}",
                    mention.getParticipantId(), message.getId());
    }

    /**
     * Creates mention notification payload.
     */
    private Map<String, Object> createMentionNotification(MentionDto mention, ChatMessageResponse message) {
        Map<String, Object> notification = new HashMap<>();
        notification.put("type", MENTION_TYPE);
        notification.put("message", message);
        notification.put("mentioned_by", message.getSender());
        notification.put("timestamp", LocalDateTime.now());
        return notification;
    }

    /**
     * Processes email notification for external participants.
     */
    private void processEmailNotificationForExternalParticipant(MentionDto mention, ChatMessageResponse message) {
        if (mention.getIsExternal() != null && mention.getIsExternal()) {
            HubParticipant participant = participantRepository.findById(mention.getParticipantId());
            if (participant != null && participant.getEmail() != null) {
                sendEmailNotificationAsync(participant, message);
            }
        }
    }

    /**
     * Sends email notification asynchronously.
     */
    @Async
    private void sendEmailNotificationAsync(HubParticipant participant, ChatMessageResponse message) {
        try {
            sendMentionEmailNotification(participant, message);
            logger.info("Sent email notification to external participant {} for mention",
                       participant.getEmail());
        } catch (Exception e) {
            logger.error("Failed to send email notification to external participant {}: {}",
                        participant.getEmail(), e.getMessage());
        }
    }

    /**
     * Logs successful mention notification processing.
     */
    private void logMentionNotificationSuccess(ChatMessageResponse message) {
        logger.info("Successfully sent mention notifications for {} mentions in message {}",
                   message.getMentions().size(), message.getId());
    }

    /**
     * Sends email notification to external participant when mentioned.
     */
    private void sendMentionEmailNotification(HubParticipant participant, ChatMessageResponse message) {
        String participantName = getParticipantNameForEmail(participant);
        String emailBody = String.format(MENTION_EMAIL_TEMPLATE, participantName, message.getContent());

        emailService.sendSimpleEmail(participant.getEmail(), MENTION_EMAIL_SUBJECT, emailBody);
    }

    /**
     * Gets participant name for email greeting.
     */
    private String getParticipantNameForEmail(HubParticipant participant) {
        return participant.getName() != null ? participant.getName() : DEFAULT_GREETING_NAME;
    }

    /**
     * Broadcasts typing indicator to channel participants by email.
     *
     * @param channelId the channel ID
     * @param userContext the typing participant email
     */
    public void broadcastTypingIndicator(@NotNull Long channelId, UserContext userContext) {
        Long participantId = resolveParticipantIdByEmail(userContext.getEmail());
        broadcastTypingIndicator(channelId, participantId, userContext.getEmail());
    }

    /**
     * Broadcasts typing indicator to channel participants.
     *
     * @param channelId the channel ID
     * @param participantId the typing participant ID
     * @param participantEmail the typing participant email
     */
    public void broadcastTypingIndicator(@NotNull Long channelId,
                                        @NotNull Long participantId,
                                        @NotBlank String participantEmail) {
        logger.debug("Broadcasting typing indicator for participant {} in channel {}",
                    participantId, channelId);

        Map<String, Object> typingData = createTypingIndicatorData(participantId, participantEmail);
        String destination = String.format(TYPING_TOPIC_PATTERN, channelId);
        messagingTemplate.convertAndSend(destination, typingData);
    }

    /**
     * Creates typing indicator data payload.
     */
    private Map<String, Object> createTypingIndicatorData(Long participantId, String participantEmail) {
        Map<String, Object> typingData = new HashMap<>();
        typingData.put("type", TYPING_TYPE);
        typingData.put("participant_id", participantId);
        typingData.put("participant_email", participantEmail);
        typingData.put("timestamp", LocalDateTime.now());
        return typingData;
    }

    /**
     * Broadcasts user joined notification to channel participants by email.
     *
     * @param channelId the channel ID
     * @param participantEmail the participant email
     */
    public void broadcastUserJoined(@NotNull Long channelId, @NotBlank String participantEmail) {
        Long participantId = resolveParticipantIdByEmail(participantEmail);
        broadcastUserJoined(channelId, participantId, participantEmail);
    }

    /**
     * Broadcasts user joined notification to channel participants.
     *
     * @param channelId the channel ID
     * @param participantId the participant ID who joined
     * @param participantEmail the participant email
     */
    public void broadcastUserJoined(@NotNull Long channelId,
                                   @NotNull Long participantId,
                                   @NotBlank String participantEmail) {
        logger.info("Broadcasting user joined for participant {} in channel {}",
                   participantId, channelId);

        Map<String, Object> joinData = createPresenceData(USER_JOINED_TYPE, participantId, participantEmail);
        broadcastPresenceNotification(channelId, joinData);
    }

    /**
     * Broadcasts user left notification to channel participants by email.
     *
     * @param channelId the channel ID
     * @param participantEmail the participant email
     */
    public void broadcastUserLeft(@NotNull Long channelId, @NotBlank String participantEmail) {
        Long participantId = resolveParticipantIdByEmail(participantEmail);
        broadcastUserLeft(channelId, participantId, participantEmail);
    }

    /**
     * Broadcasts user left notification to channel participants.
     *
     * @param channelId the channel ID
     * @param participantId the participant ID who left
     * @param participantEmail the participant email
     */
    public void broadcastUserLeft(@NotNull Long channelId,
                                 @NotNull Long participantId,
                                 @NotBlank String participantEmail) {
        logger.info("Broadcasting user left for participant {} in channel {}",
                   participantId, channelId);

        Map<String, Object> leaveData = createPresenceData(USER_LEFT_TYPE, participantId, participantEmail);
        broadcastPresenceNotification(channelId, leaveData);
    }

    /**
     * Creates presence notification data.
     */
    private Map<String, Object> createPresenceData(String type, Long participantId, String participantEmail) {
        Map<String, Object> presenceData = new HashMap<>();
        presenceData.put("type", type);
        presenceData.put("participant_id", participantId);
        presenceData.put("participant_email", participantEmail);
        presenceData.put("timestamp", LocalDateTime.now());
        return presenceData;
    }

    /**
     * Broadcasts presence notification to channel.
     */
    private void broadcastPresenceNotification(Long channelId, Map<String, Object> presenceData) {
        String destination = String.format(PRESENCE_TOPIC_PATTERN, channelId);
        messagingTemplate.convertAndSend(destination, presenceData);
    }

    /**
     * Sends error message to a specific user.
     *
     * @param userEmail the user email
     * @param errorMessage the error message
     */
    public void sendErrorToUser(@NotBlank String userEmail, @NotBlank String errorMessage) {
        logger.warn("Sending error message to user {}: {}", userEmail, errorMessage);

        Map<String, Object> errorData = createErrorData(errorMessage);
        String destination = String.format(ERROR_QUEUE_PATTERN, userEmail);
        messagingTemplate.convertAndSend(destination, errorData);
    }

    /**
     * Creates error notification data.
     */
    private Map<String, Object> createErrorData(String errorMessage) {
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("type", ERROR_TYPE);
        errorData.put("message", errorMessage);
        errorData.put("timestamp", LocalDateTime.now());
        return errorData;
    }

    /**
     * Gets all participants who have access to a channel based on its scope.
     */
    private List<HubParticipant> getChannelParticipants(ChatChannel channel) {
        List<HubParticipant> allParticipants = participantRepository
                .findActiveParticipantsByHubId(channel.getHubId());

        return allParticipants.stream()
                .filter(participant -> hasAccessToChannel(participant, channel))
                .toList();
    }

    /**
     * Checks if a participant has access to a channel based on the channel scope.
     */
    private boolean hasAccessToChannel(HubParticipant participant, ChatChannel channel) {
        return switch (channel.getScope().name()) {
            case "general" -> true; // All hub participants can access general channels
            case "custom" -> hasCustomChannelAccess(participant.getId(), channel.getId());
            default -> false; // Unknown channel type
        };
    }



    /**
     * Checks if participant is admin, reviewer, or the specific creator.
     */
    private boolean hasCustomChannelAccess(Long participantId, Long channelId) {
        // Check if participant is in the chat_channel_participants table for this channel
        return dsl.fetchExists(
            dsl.selectOne()
                .from(CHAT_CHANNEL_PARTICIPANTS)
                .where(CHAT_CHANNEL_PARTICIPANTS.CHANNEL_ID.eq(channelId))
                .and(CHAT_CHANNEL_PARTICIPANTS.PARTICIPANT_ID.eq(participantId))
        );
    }

    /**
     * Resolves participant ID by email address.
     *
     * @param email the participant email
     * @return the participant ID
     * @throws RuntimeException if participant not found
     */
    private Long resolveParticipantIdByEmail(String email) {
        return participantRepository.findByEmail(email)
                .map(HubParticipant::getId)
                .orElseThrow(() -> new RuntimeException(String.format(PARTICIPANT_NOT_FOUND_MESSAGE, email)));
    }
}
