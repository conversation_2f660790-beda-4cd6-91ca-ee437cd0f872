package com.collabhub.be.modules.auth.repository;

import com.collabhub.be.modules.auth.dto.RedirectContext;
import com.collabhub.be.modules.auth.model.TokenType;
import com.collabhub.be.util.JsonbUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.VerificationTokenDao;
import org.jooq.generated.tables.records.VerificationTokenRecord;
import org.jooq.generated.tables.pojos.VerificationToken;
import org.springframework.stereotype.Repository;

import static org.jooq.generated.tables.VerificationToken.VERIFICATION_TOKEN;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Repository for VerificationToken entity using jOOQ for database operations.
 * Handles email verification tokens, magic links, and password reset tokens.
 */
@Repository
public class VerificationTokenRepository extends VerificationTokenDao {

    private final DSLContext dsl;
    private final ObjectMapper objectMapper;

    public VerificationTokenRepository(DSLContext dsl, ObjectMapper objectMapper) {
        super(dsl.configuration());
        this.dsl = dsl;
        this.objectMapper = objectMapper;
    }

    /**
     * Finds a verification token by token string and type.
     *
     * @param token the token string
     * @param tokenType the token type
     * @return Optional containing the verification token if found and not expired/revoked
     */
    public Optional<VerificationToken> findByTokenAndType(String token, TokenType tokenType) {
        return dsl.select()
                .from(VERIFICATION_TOKEN)
                .where(VERIFICATION_TOKEN.TOKEN.eq(token))
                .and(VERIFICATION_TOKEN.TOKEN_TYPE.eq(tokenType.getTypeName()))
                .and(VERIFICATION_TOKEN.EXPIRES_AT.gt(LocalDateTime.now()))
                .and(VERIFICATION_TOKEN.REVOKED.eq(false))
                .fetchOptionalInto(VerificationToken.class);
    }

    /**
     * Creates a new verification token.
     *
     * @param userId the user ID
     * @param token the token string
     * @param tokenType the token type
     * @param expiresAt the expiration timestamp
     * @return the created verification token
     */
    public VerificationToken createToken(Long userId, String token, TokenType tokenType, LocalDateTime expiresAt) {
        VerificationTokenRecord record = dsl.newRecord(VERIFICATION_TOKEN);
        record.setUserId(userId);
        record.setToken(token);
        record.setTokenType(tokenType.getTypeName());
        record.setExpiresAt(expiresAt);
        record.setRevoked(false);
        record.setCreatedAt(LocalDateTime.now());

        record.store();
        return record.into(VerificationToken.class);
    }

    /**
     * Revokes a verification token by marking it as used.
     *
     * @param token the token string
     * @return true if token was revoked, false if not found
     */
    public boolean revokeToken(String token) {
        int updated = dsl.update(VERIFICATION_TOKEN)
                .set(VERIFICATION_TOKEN.REVOKED, true)
                .where(VERIFICATION_TOKEN.TOKEN.eq(token))
                .and(VERIFICATION_TOKEN.REVOKED.eq(false))
                .execute();

        return updated > 0;
    }

    /**
     * Deletes expired verification tokens for cleanup.
     *
     * @return number of deleted tokens
     */
    public int deleteExpiredTokens() {
        return dsl.deleteFrom(VERIFICATION_TOKEN)
                .where(VERIFICATION_TOKEN.EXPIRES_AT.lt(LocalDateTime.now()))
                .execute();
    }

    /**
     * Finds all non-revoked tokens for a user and token type.
     *
     * @param userId the user ID
     * @param tokenType the token type
     * @return list of verification tokens
     */
    public java.util.List<VerificationToken> findByUserIdAndType(Long userId, TokenType tokenType) {
        return dsl.select()
                .from(VERIFICATION_TOKEN)
                .where(VERIFICATION_TOKEN.USER_ID.eq(userId))
                .and(VERIFICATION_TOKEN.TOKEN_TYPE.eq(tokenType.getTypeName()))
                .and(VERIFICATION_TOKEN.REVOKED.eq(false))
                .fetchInto(VerificationToken.class);
    }

    /**
     * Creates a magic link token for external users (email-based).
     *
     * @param email the external user's email
     * @param accountId the account ID for multi-tenancy
     * @param token the token string
     * @param expiresAt the expiration timestamp
     * @return the created verification token
     */
    public VerificationToken createExternalMagicLinkToken(String email, Long accountId,
                                                         String token, LocalDateTime expiresAt) {
        return createExternalMagicLinkTokenWithRedirect(email, accountId, token, expiresAt, null);
    }

    /**
     * Creates a magic link token for external users with redirect context.
     *
     * @param email the external user's email
     * @param accountId the account ID for multi-tenancy
     * @param token the token string
     * @param expiresAt the expiration timestamp
     * @param redirectContext the redirect context for post-authentication navigation
     * @return the created verification token
     */
    public VerificationToken createExternalMagicLinkTokenWithRedirect(String email, Long accountId,
                                                                     String token, LocalDateTime expiresAt,
                                                                     RedirectContext redirectContext) {
        VerificationTokenRecord record = dsl.newRecord(VERIFICATION_TOKEN);
        record.setUserId(null); // External users don't have user_id
        record.setEmail(email);
        record.setAccountId(accountId);
        record.setToken(token);
        record.setTokenType(TokenType.MAGIC_LINK.getTypeName());
        record.setExpiresAt(expiresAt);
        record.setRevoked(false);
        record.setCreatedAt(LocalDateTime.now());

        // Set redirect context if provided
        if (redirectContext != null) {
            record.setRedirectType(redirectContext.getType().getValue());
            record.setRedirectContext(JsonbUtil.toJsonb(redirectContext, objectMapper));
        }

        record.store();
        return record.into(VerificationToken.class);
    }

    /**
     * Revokes all magic link tokens for an external user email in an account.
     *
     * @param email the email address
     * @param accountId the account ID
     * @return true if any tokens were revoked
     */
    public boolean revokeExternalMagicLinkTokens(String email, Long accountId) {
        int updated = dsl.update(VERIFICATION_TOKEN)
                .set(VERIFICATION_TOKEN.REVOKED, true)
                .where(VERIFICATION_TOKEN.EMAIL.eq(email))
                .and(VERIFICATION_TOKEN.ACCOUNT_ID.eq(accountId))
                .and(VERIFICATION_TOKEN.TOKEN_TYPE.eq(TokenType.MAGIC_LINK.getTypeName()))
                .and(VERIFICATION_TOKEN.REVOKED.eq(false))
                .execute();

        return updated > 0;
    }
}
