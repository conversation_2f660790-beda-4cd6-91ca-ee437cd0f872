package com.collabhub.be.modules.auth.repository;

import org.jooq.DSLContext;
import org.jooq.generated.tables.daos.UserDao;
import org.jooq.generated.tables.pojos.User;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static org.jooq.generated.tables.User.USER;

/**
 * Repository for User entity using jOOQ for database operations.
 * Provides multi-tenant aware queries scoped by account_id.
 */
@Repository
public class UserRepository extends UserDao {

    private final DSLContext dsl;

    public UserRepository(DSLContext dsl) {
        super(dsl.configuration());
        this.dsl = dsl;
    }

    public Optional<User> findByEmail(String email) {
        return dsl.select()
                .from(USER)
                .where(USER.EMAIL.eq(email))
                .and(USER.ENABLED.eq(true))
                .fetchOptionalInto(User.class);
    }


    public User findById(Long id) {
        return dsl.select()
                .from(USER)
                .where(USER.ID.eq(id))
                .and(USER.ENABLED.eq(true))
                .fetchOneInto(User.class);
    }

    /**
     * Finds a user by ID without checking enabled status.
     * Used for internal operations like email verification.
     *
     * @param id the user ID
     * @return the user or null if not found
     */
    public User findByIdIncludingDisabled(Long id) {
        return dsl.select()
                .from(USER)
                .where(USER.ID.eq(id))
                .fetchOneInto(User.class);
    }

    /**
     * Enables a user account by setting enabled=true.
     *
     * @param userId the user ID to enable
     * @return true if user was enabled, false if not found
     */
    public boolean enableUser(Long userId) {
        int updated = dsl.update(USER)
                .set(USER.ENABLED, true)
                .where(USER.ID.eq(userId))
                .and(USER.ENABLED.eq(false))
                .execute();

        return updated > 0;
    }

    /**
     * Validates that a user's role belongs to their account.
     * Used for account-level role validation in permission checks.
     *
     * @param userId the user ID
     * @param accountId the account ID
     * @param role the role to validate
     * @return true if the user has the specified role in the account, false otherwise
     */
    public boolean validateAccountRoleOwnership(Long userId, Long accountId, String role) {
        return dsl.fetchExists(
                dsl.selectOne()
                        .from(USER)
                        .where(USER.ID.eq(userId))
                        .and(USER.ACCOUNT_ID.eq(accountId))
                        .and(USER.ROLE.eq(role))
                        .and(USER.ENABLED.eq(true))
        );
    }
}
