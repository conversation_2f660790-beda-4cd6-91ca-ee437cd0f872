package com.collabhub.be.modules.posts.service;

import com.collabhub.be.config.S3Properties;
import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;
import com.collabhub.be.exception.ForbiddenException;
import com.collabhub.be.exception.NotFoundException;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.collaborationhub.model.ParticipantDetails;
import com.collabhub.be.modules.collaborationhub.repository.CollaborationHubRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.service.CollabHubPermissionService;
import com.collabhub.be.modules.collaborationhub.service.HubParticipantManagementService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.posts.converter.PostConverter;
import com.collabhub.be.modules.posts.dto.*;
import com.collabhub.be.modules.posts.repository.PostRepositoryImpl;
import com.collabhub.be.modules.posts.repository.PostCommentRepositoryImpl;
import com.collabhub.be.modules.posts.util.PostParticipantUtil;
import com.collabhub.be.modules.posts.util.PostMediaUtil;
import com.collabhub.be.modules.posts.repository.PostReviewerRepositoryImpl;
import com.collabhub.be.modules.media.service.MediaService;
import com.collabhub.be.modules.media.repository.PostMediaRepositoryImpl;
import com.collabhub.be.modules.media.repository.MediaRepositoryImpl;
import com.collabhub.be.modules.media.dto.MediaDto;
import com.collabhub.be.service.s3.S3StorageService;
import org.jooq.generated.enums.ReviewStatus;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.jooq.generated.tables.pojos.Post;
import org.jooq.generated.tables.pojos.Media;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.web.multipart.MultipartFile;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.annotation.Counted;

import java.util.List;
import java.util.Map;

/**
 * Service for managing posts in collaboration hubs.
 * Handles post creation, updates, media uploads, and access control.
 */
@Service
@Validated
public class PostService {

    private static final Logger logger = LoggerFactory.getLogger(PostService.class);

    private final PostRepositoryImpl postRepository;
    private final PostCommentRepositoryImpl postCommentRepository;
    private final PostReviewerRepositoryImpl postReviewerRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final PostConverter postConverter;
    private final S3StorageService s3StorageService;
    private final MediaService mediaService;
    private final PostMediaRepositoryImpl postMediaRepository;
    private final MediaRepositoryImpl mediaRepository;
    private final PostReviewService postReviewService;
    private final PostPermissionService postPermissionService;
    private final CollabHubPermissionService collabHubPermissionService;
    private final PostParticipantUtil participantUtil;
    private final PostMediaUtil mediaUtil;
    private final JwtClaimsService jwtClaimsService;
    private final HubParticipantManagementService hubParticipantManagementService;
    private final CollaborationHubRepositoryImpl hubRepository;

    public PostService(PostRepositoryImpl postRepository,
                      PostCommentRepositoryImpl postCommentRepository,
                      PostReviewerRepositoryImpl postReviewerRepository,
                      HubParticipantRepositoryImpl participantRepository,
                      PostConverter postConverter,
                      S3StorageService s3StorageService,
                      MediaService mediaService,
                      PostMediaRepositoryImpl postMediaRepository,
                      MediaRepositoryImpl mediaRepository,
                      PostReviewService postReviewService,
                      PostPermissionService postPermissionService,
                      CollabHubPermissionService collabHubPermissionService,
                      PostParticipantUtil participantUtil,
                      PostMediaUtil mediaUtil,
                      JwtClaimsService jwtClaimsService,
                      HubParticipantManagementService hubParticipantManagementService,
                      CollaborationHubRepositoryImpl hubRepository) {
        this.postRepository = postRepository;
        this.postCommentRepository = postCommentRepository;
        this.postReviewerRepository = postReviewerRepository;
        this.participantRepository = participantRepository;
        this.postConverter = postConverter;
        this.s3StorageService = s3StorageService;
        this.mediaService = mediaService;
        this.postMediaRepository = postMediaRepository;
        this.mediaRepository = mediaRepository;
        this.postReviewService = postReviewService;
        this.postPermissionService = postPermissionService;
        this.collabHubPermissionService = collabHubPermissionService;
        this.participantUtil = participantUtil;
        this.mediaUtil = mediaUtil;
        this.jwtClaimsService = jwtClaimsService;
        this.hubParticipantManagementService = hubParticipantManagementService;
        this.hubRepository = hubRepository;
    }

    /**
     * Creates a new post in a collaboration hub.
     *
     * @param hubId the collaboration hub ID
     * @param request the post creation request
     * @return the created post response
     */
    @Transactional
    @Timed(value = "post.create", description = "Time taken to create a post")
    @Counted(value = "post.create.attempts", description = "Number of post creation attempts")
    public PostResponse createPost(@NotNull Long hubId, @Valid @NotNull PostCreateRequest request) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug("Creating post in hub {} by user {}", hubId, userContext.getUserId());

        HubParticipant participant = validatePostCreation(hubId);
        Post post = createPostEntity(request, hubId, participant.getId());
        Long accountId = extractAccountIdFromHub(hubId);
        processPostCreationExtras(post.getId(), hubId, request, accountId);
        logPostCreation(post.getId(), hubId, userContext.getUserId(), accountId, request);

        return getPostDetails(post.getId());
    }

    /**
     * Creates the post entity and persists it to the database.
     */
    private Post createPostEntity(PostCreateRequest request, Long hubId, Long creatorParticipantId) {
        Post post = postConverter.toPost(request, hubId, creatorParticipantId);
        postRepository.insert(post);
        return post;
    }

    /**
     * Processes media associations and reviewer assignments for a new post.
     */
    private void processPostCreationExtras(Long postId, Long hubId, PostCreateRequest request, Long accountId) {
        processMediaAssociations(postId, request.getMediaUris(), accountId);
        postReviewService.assignReviewersToPost(postId, hubId, request.getReviewerIds());
    }

    /**
     * Logs post creation with relevant metrics.
     */
    private void logPostCreation(Long postId, Long hubId, Long userId, Long accountId, PostCreateRequest request) {
        logger.info("POST_CREATED: postId={}, hubId={}, userId={}, accountId={}, hasMedia={}, hasReviewers={}",
                   postId, hubId, userId, accountId,
                   (request.getMediaUris() != null && !request.getMediaUris().isEmpty()),
                   (request.getReviewerIds() != null && !request.getReviewerIds().isEmpty()));
    }

    /**
     * Gets a paginated list of posts with role-based filtering.
     *
     * @param hubId the collaboration hub ID
     * @param pageRequest the pagination request
     * @param filter the filter criteria
     * @param status the review status filter
     * @return paginated list of posts
     */
    @Transactional(readOnly = true)
    @Timed(value = "post.list", description = "Time taken to list posts")
    public PostListResponse getPosts(@NotNull Long hubId, @Valid @NotNull PageRequest pageRequest,
                                    @NotNull PostFilter filter, ReviewStatus status) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug("Retrieving posts for hub {} with filter: {}, status: {}", hubId, filter.getValue(), status);

        collabHubPermissionService.validateCanParticipantAccessHubContent(hubId);

        int offset = pageRequest.getOffset();
        List<Post> posts = fetchPosts(hubId, userContext, filter.getValue(), status, offset, pageRequest.getSize());
        long totalElements = countPosts(hubId, userContext, filter.getValue(), status);
        List<PostListItemResponse> postItems = convertPostsToListItems(posts, userContext);

        return new PostListResponse(postItems, pageRequest, totalElements);
    }

    /**
     * Gets detailed information about a specific post.
     *
     * @param postId the post ID to retrieve
     * @return detailed post response
     */
    @Transactional(readOnly = true)
    public PostResponse getPostDetails(@NotNull Long postId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug("Retrieving post details for post {} by user {}", postId, userContext.getUserId());

        postPermissionService.validateCanViewPost(postId);
        Post post = findPostByIdOrThrow(postId);
        PostDetailData detailData = gatherPostDetailData(post);

        return postConverter.toResponse(
            post, detailData.mediaList(), detailData.creator(),
            detailData.reviewers(), detailData.permissions()
        );
    }

    /**
     * Gathers all data needed for post details response.
     */
    private PostDetailData gatherPostDetailData(Post post) {
        List<Media> mediaList = mediaService.findMediaByPostId(post.getId());
        PostResponse.PostCreator creator = participantUtil.createPostCreator(post.getCreatorParticipantId());
        List<PostResponse.PostReviewer> reviewers = postReviewService.getPostReviewers(post.getId());
        PostResponse.PostPermissions permissions = createPostPermissions(post);

        return new PostDetailData(mediaList, creator, reviewers, permissions);
    }

    /**
     * Record to hold post detail data.
     */
    private record PostDetailData(
        List<Media> mediaList,
        PostResponse.PostCreator creator,
        List<PostResponse.PostReviewer> reviewers,
        PostResponse.PostPermissions permissions
    ) {}

    /**
     * Record to hold bulk-loaded data for post list items.
     */
    private record BulkLoadedData(
        Map<Long, ParticipantDetails> creatorDetailsMap,
        Map<Long, List<Media>> postMediaMap,
        Map<Long, List<PostResponse.PostReviewer>> postReviewersMap,
        Map<Long, Integer> commentCountsMap,
        Map<Long, PostPermissionService.PostListPermissions> postPermissionsMap
    ) {}

    /**
     * Updates an existing post.
     *
     * @param postId the post ID to update
     * @param request the post update request
     * @return updated post response
     */
    @Transactional
    public PostResponse updatePost(@NotNull Long postId, @Valid @NotNull PostUpdateRequest request) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug("Updating post {} by user {}", postId, userContext.getUserId());

        Post post = findAndValidatePostForEdit(postId);
        Long accountId = extractAccountIdFromHub(post.getHubId());
        updatePostMedia(postId, request, accountId);
        updatePostReviewers(postId, post.getHubId(), request);
        updatePostEntity(post, request);
        logPostUpdate(postId, userContext.getUserId(), accountId, request);

        return getPostDetails(postId);
    }

    /**
     * Finds a post and validates user can edit it.
     */
    private Post findAndValidatePostForEdit(Long postId) {
        Post post = findPostByIdOrThrow(postId);
        postPermissionService.validateCanEditPost(post);
        return post;
    }

    /**
     * Updates post media associations if provided in the request.
     */
    private void updatePostMedia(Long postId, PostUpdateRequest request, Long accountId) {
        if (request.getMediaUris() != null) {
            List<Long> mediaIds = request.getMediaUris().isEmpty() ?
                List.of() : mediaUtil.createOrValidateMediaRecords(request.getMediaUris(), accountId);
            postMediaRepository.updatePostMediaAssociations(postId, mediaIds);
        }
    }

    /**
     * Updates post reviewer assignments if provided in the request.
     * Only ADMIN and post creator can update reviewers.
     */
    private void updatePostReviewers(Long postId, Long hubId, PostUpdateRequest request) {
        if (request.getReviewerIds() != null) {
            validateReviewerUpdatePermission(postId);
            postReviewService.updatePostReviewers(postId, hubId, request.getReviewerIds());
        }
    }

    /**
     * Validates that the user has permission to update reviewers for this post.
     * Only ADMIN and post creator can update reviewers.
     */
    private void validateReviewerUpdatePermission(Long postId) {
        Post post = findPostByIdOrThrow(postId);
        postPermissionService.validateCanEditPost(post);
    }

    /**
     * Updates the post entity with new data.
     */
    private void updatePostEntity(Post post, PostUpdateRequest request) {
        postConverter.updatePost(post, request);
        postRepository.update(post);
    }

    /**
     * Logs post update with relevant metrics.
     */
    private void logPostUpdate(Long postId, Long userId, Long accountId, PostUpdateRequest request) {
        logger.info("POST_UPDATED: postId={}, userId={}, accountId={}, hasMediaUpdate={}, hasReviewerUpdate={}",
                   postId, userId, accountId,
                   (request.getMediaUris() != null && !request.getMediaUris().isEmpty()),
                   (request.getReviewerIds() != null));
    }

    /**
     * Deletes a post.
     *
     * @param postId the post ID to delete
     */
    @Transactional
    @Timed(value = "post.delete", description = "Time taken to delete a post")
    @Counted(value = "post.delete.attempts", description = "Number of post deletion attempts")
    public void deletePost(@NotNull Long postId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        logger.debug("Deleting post {} by user {}", postId, userContext.getUserId());

        Post post = findAndValidatePostForDeletion(postId);
        Long accountId = extractAccountIdFromHub(post.getHubId());
        performPostDeletion(post, accountId);
        logPostDeletion(postId, userContext.getUserId(), accountId, post.getHubId());
    }

    /**
     * Finds a post and validates user can delete it.
     */
    private Post findAndValidatePostForDeletion(Long postId) {
        Post post = findPostByIdOrThrow(postId);
        postPermissionService.validateCanEditPost(post);
        return post;
    }

    /**
     * Validates that a post exists (without account validation).
     *
     * @param postId the post ID
     * @return the post entity
     * @throws NotFoundException if post is not found
     */
    public Post findPostByIdOrThrow(Long postId) {
        Post post = postRepository.findById(postId);
        if (post == null) {
            logger.warn("Post not found: ID={}", postId);
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE + ": " + postId);
        }
        return post;
    }
    
    /**
     * Performs the actual post deletion including media cleanup.
     */
    private void performPostDeletion(Post post, Long accountId) {
        deleteAssociatedMediaFiles(post, accountId);

        boolean deleted = postRepository.softDeletePost(post.getId());
        if (!deleted) {
            throw new NotFoundException(ErrorCode.RESOURCE_NOT_FOUND, PostConstants.POST_NOT_FOUND_MESSAGE + ": " + post.getId());
        }
    }

    /**
     * Logs post deletion with relevant metrics.
     */
    private void logPostDeletion(Long postId, Long userId, Long accountId, Long hubId) {
        logger.info("POST_DELETED: postId={}, userId={}, accountId={}, hubId={}",
                   postId, userId, accountId, hubId);
    }

    /**
     * Uploads a media file for posts using the new media architecture.
     *
     * @param file the media file to upload
     * @param accountId the account ID for multi-tenant storage
     * @return file upload response with URL and metadata
     */
    @Transactional
    public FileUploadResponse uploadMedia(@Valid @NotNull MultipartFile file, @NotNull Long accountId) {
        logger.debug("Uploading media file: {} for account {}", file.getOriginalFilename(), accountId);

        MediaDto mediaDto = mediaService.uploadMedia(file, accountId, S3Properties.ResourceType.POSTS);
        FileType type = FileType.fromMimeType(file.getContentType());

        FileUploadResponse response = new FileUploadResponse(
                mediaDto.getUrl(),
                file.getOriginalFilename(),
                file.getSize(),
                file.getContentType(),
                type
        );

        logger.info("Successfully uploaded media file: {} with ID: {} for account {}",
                   mediaDto.getUrl(), mediaDto.getId(), accountId);
        return response;
    }

    // Private helper methods

    /**
     * Validates post creation requirements and returns participant info.
     * For app-level ADMINs, automatically creates hub admin participant if not already present.
     */
    private HubParticipant validatePostCreation(Long hubId) {
        UserContext userContext = jwtClaimsService.getCurrentUser();
        postPermissionService.validateCanCreatePost(hubId);

        // Try to get existing participant, or create admin participant if user is app-level ADMIN
        HubParticipant participant = hubParticipantManagementService.ensureAdminParticipantExists(hubId, userContext);

        if (participant == null) {
            // User is not a participant and not an app-level ADMIN
            throw new ForbiddenException(ErrorCode.HUB_ACCESS_DENIED,
                    "User is not a participant in this collaboration hub");
        }

        return participant;
    }

    /**
     * Processes media associations for a post.
     */
    private void processMediaAssociations(Long postId, List<String> mediaUris, Long accountId) {
        if (mediaUtil.hasValidMediaUris(mediaUris)) {
            List<Long> mediaIds = mediaUtil.createOrValidateMediaRecords(mediaUris, accountId);
            postMediaRepository.createPostMediaAssociations(postId, mediaIds);
        }
    }

    /**
     * Deletes associated media files from S3.
     * Media files are now managed through the post_media junction table.
     */
    private void deleteAssociatedMediaFiles(Post post, Long accountId) {
        // Get media files associated with this post through junction table
        List<Media> postMedia = mediaRepository.findByPostId(post.getId());
        for (Media media : postMedia) {
            try {
                // Generate the full file URL for deletion
                String fileUrl = s3StorageService.generateFileUrl(media.getS3Bucket(), media.getS3Key());
                s3StorageService.deleteFile(fileUrl, accountId);
                logger.info("Deleted media file: {}", media.getS3Key());
            } catch (Exception e) {
                logger.warn("Failed to delete media file {}: {}", media.getS3Key(), e.getMessage());
            }
        }
    }

    /**
     * Creates post permissions using the converter.
     */
    private PostResponse.PostPermissions createPostPermissions(Post post) {
        boolean canEdit = postPermissionService.canUserEditPost(post);
        boolean canReview = postPermissionService.canUserReviewPost(post);
        boolean canComment = postPermissionService.canUserCommentOnPost(post);

        return postConverter.createPostPermissions(canEdit, canReview, canComment);
    }

    /**
     * Fetches posts with role-based access.
     */
    private List<Post> fetchPosts(Long hubId, UserContext userContext, String filter,
                                 ReviewStatus status, int offset, int size) {
        return postRepository.findPosts(hubId, userContext.getUserId(), filter, status, offset, size);
    }

    /**
     * Counts posts with role-based access.
     */
    private long countPosts(Long hubId, UserContext userContext, String filter, ReviewStatus status) {
        return postRepository.countPosts(hubId, userContext.getUserId(), filter, status);
    }

    /**
     * Converts posts to list item responses with bulk loading to avoid N+1 queries.
     */
    private List<PostListItemResponse> convertPostsToListItems(List<Post> posts, UserContext userContext) {
        if (posts.isEmpty()) {
            return List.of();
        }

        BulkLoadedData bulkData = loadBulkDataForPosts(posts, userContext);

        return posts.stream()
                .map(post -> buildPostListItem(post, bulkData, userContext))
                .toList();
    }

    /**
     * Loads all bulk data needed for post list items to avoid N+1 queries.
     */
    private BulkLoadedData loadBulkDataForPosts(List<Post> posts, UserContext userContext) {
        List<Long> postIds = posts.stream().map(Post::getId).toList();
        List<Long> creatorParticipantIds = posts.stream()
                .map(Post::getCreatorParticipantId)
                .distinct()
                .toList();

        Map<Long, ParticipantDetails> creatorDetailsMap =
                participantRepository.findParticipantDetailsByIds(creatorParticipantIds);
        Map<Long, List<Media>> postMediaMap = bulkLoadPostMedia(postIds);
        Map<Long, List<PostResponse.PostReviewer>> postReviewersMap =
                postReviewService.bulkLoadPostReviewers(postIds);
        Map<Long, Integer> commentCountsMap =
                postCommentRepository.bulkCountCommentsByPostIds(postIds, userContext.getAccountId());
        Map<Long, PostPermissionService.PostListPermissions> postPermissionsMap =
                calculateBulkPostPermissions(posts);

        return new BulkLoadedData(creatorDetailsMap, postMediaMap, postReviewersMap,
                                 commentCountsMap, postPermissionsMap);
    }

    /**
     * Builds a single PostListItemResponse from bulk-loaded data.
     */
    private PostListItemResponse buildPostListItem(Post post, BulkLoadedData bulkData, UserContext userContext) {
        PostListItemResponse.PostCreator creator = participantUtil.createListItemCreator(
                post.getCreatorParticipantId(), bulkData.creatorDetailsMap());

        List<Media> mediaList = bulkData.postMediaMap().getOrDefault(post.getId(), List.of());

        List<PostResponse.PostReviewer> reviewers = bulkData.postReviewersMap().getOrDefault(post.getId(), List.of());
        List<PostListItemResponse.PostReviewer> listItemReviewers = convertToListItemReviewers(reviewers);

        int commentCount = bulkData.commentCountsMap().getOrDefault(post.getId(), 0);
        PostPermissionService.PostListPermissions permissions = bulkData.postPermissionsMap().get(post.getId());
        ReviewStatus myReviewStatus = calculateMyReviewStatus(post.getId(), userContext.getUserId());

        return postConverter.toListItem(
                post, mediaList, creator, myReviewStatus, commentCount,
                listItemReviewers,
                permissions.canEdit(), permissions.canReview(), permissions.canComment()
        );
    }

    /**
     * Converts PostResponse.PostReviewer list to PostListItemResponse.PostReviewer list.
     */
    private List<PostListItemResponse.PostReviewer> convertToListItemReviewers(List<PostResponse.PostReviewer> reviewers) {
        return reviewers.stream()
                .map(reviewer -> postConverter.createEnhancedListItemReviewer(
                        reviewer.getId(), reviewer.getName(), reviewer.getEmail(), reviewer.getStatus(),
                        reviewer.getReviewNotes(), reviewer.getAssignedAt(), reviewer.getReviewedAt()))
                .toList();
    }

    /**
     * Calculates the current user's review status for a post.
     */
    private ReviewStatus calculateMyReviewStatus(Long postId, Long userId) {
        if (userId == null) {
            return null;
        }

        Post post = postRepository.findById(postId);
        if (post == null) {
            return null;
        }

        HubParticipant participant = postRepository.getUserParticipant(post.getHubId(), userId);
        if (participant == null) {
            return null;
        }

        return postReviewerRepository.findByPostIdAndParticipantId(postId, participant.getId())
                .map(org.jooq.generated.tables.pojos.PostReviewer::getReviewStatus)
                .orElse(null);
    }

    /**
     * Bulk calculates permissions for multiple posts to avoid N+1 queries.
     */
    private Map<Long, PostPermissionService.PostListPermissions> calculateBulkPostPermissions(List<Post> posts) {
        return postPermissionService.calculateBulkPostPermissions(posts);
    }

    /**
     * Bulk loads media for multiple posts to avoid N+1 queries.
     * Uses a single optimized query instead of multiple individual calls.
     */
    private Map<Long, List<Media>> bulkLoadPostMedia(List<Long> postIds) {
        return mediaService.bulkFindMediaByPostIds(postIds);
    }

    /**
     * Extracts account ID from hub for multi-tenancy support.
     * This allows both internal users and external participants to work with posts.
     */
    private Long extractAccountIdFromHub(Long hubId) {
        Long accountId = hubRepository.getAccountIdForHub(hubId);
        if (accountId == null) {
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Unable to determine account for hub: " + hubId);
        }
        return accountId;
    }
}
