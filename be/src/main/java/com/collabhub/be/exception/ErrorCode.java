package com.collabhub.be.exception;

/**
 * Error codes for frontend internationalization (i18n).
 * Each code corresponds to a translation key for localized error messages.
 */
public enum ErrorCode {

    // General application errors
    INTERNAL_SERVER_ERROR("internal_server_error"),
    BAD_REQUEST("bad_request"),
    NOT_FOUND("not_found"),
    METHOD_NOT_ALLOWED("method_not_allowed"),
    UNSUPPORTED_MEDIA_TYPE("unsupported_media_type"),

    // Authentication and authorization errors
    AUTHENTICATION_REQUIRED("authentication_required"),
    AUTHENTICATION_FAILED("authentication_failed"),
    ACCESS_DENIED("access_denied"),
    TOKEN_EXPIRED("token_expired"),
    TOKEN_INVALID("token_invalid"),
    INVALID_CREDENTIALS("invalid_credentials"),
    ACCOUNT_DISABLED("account_disabled"),
    REFRESH_TOKEN_INVALID("refresh_token_invalid"),
    REFRESH_TOKEN_EXPIRED("refresh_token_expired"),
    INSUFFICIENT_PRIVILEGES("insufficient_privileges"),

    // Registration and user management errors
    EMAIL_ALREADY_EXISTS("email_already_exists"),
    REGISTRATION_FAILED("registration_failed"),
    EMAIL_VERIFICATION_FAILED("email_verification_failed"),
    VERIFICATION_TOKEN_INVALID("verification_token_invalid"),
    VERIFICATION_TOKEN_EXPIRED("verification_token_expired"),

    // Validation errors
    VALIDATION_FAILED("validation_failed"),

    // Field-level validation errors
    FIELD_REQUIRED("field_required"),
    FIELD_INVALID_FORMAT("field_invalid_format"),
    FIELD_TOO_SHORT("field_too_short"),
    FIELD_TOO_LONG("field_too_long"),
    FIELD_INVALID_LENGTH("field_invalid_length"),
    FIELD_INVALID_SIZE("field_invalid_size"),
    FIELD_INVALID_RANGE("field_invalid_range"),
    FIELD_INVALID_PATTERN("field_invalid_pattern"),
    FIELD_INVALID_EMAIL("field_invalid_email"),
    FIELD_INVALID_URL("field_invalid_url"),
    FIELD_INVALID_DATE("field_invalid_date"),
    FIELD_INVALID_NUMBER("field_invalid_number"),
    FIELD_DUPLICATE_VALUE("field_duplicate_value"),

    // Business logic errors
    RESOURCE_NOT_FOUND("resource_not_found"),
    RESOURCE_ALREADY_EXISTS("resource_already_exists"),
    OPERATION_NOT_ALLOWED("operation_not_allowed"),
    BUSINESS_RULE_VIOLATION("business_rule_violation"),

    // Multi-tenancy errors
    ACCOUNT_ACCESS_DENIED("account_access_denied"),
    INVALID_ACCOUNT_CONTEXT("invalid_account_context"),

    // Request processing errors
    INVALID_REQUEST_FORMAT("invalid_request_format"),
    MISSING_REQUEST_BODY("missing_request_body"),
    INVALID_REQUEST_PARAMETER("invalid_request_parameter"),
    REQUEST_TIMEOUT("request_timeout"),

    // Invoice-specific errors
    PDF_GENERATION_FAILED("pdf_generation_failed"),
    PDF_GENERATION_ERROR("pdf_generation_error"),
    EMAIL_DELIVERY_FAILED("email_delivery_failed"),
    VALIDATION_ERROR("validation_error"),
    DUPLICATE_RESOURCE("duplicate_resource"),
    DUPLICATE_OPERATION("duplicate_operation"),

    // Collaboration hub errors
    HUB_NOT_FOUND("hub_not_found"),
    HUB_NAME_ALREADY_EXISTS("hub_name_already_exists"),
    HUB_ACCESS_DENIED("hub_access_denied"),
    INSUFFICIENT_PERMISSIONS("insufficient_permissions"),
    PARTICIPANT_NOT_FOUND("participant_not_found"),
    PARTICIPANT_ALREADY_EXISTS("participant_already_exists"),
    INVALID_PARTICIPANT_TYPE("invalid_participant_type"),
    MAGIC_LINK_EXPIRED("magic_link_expired"),
    INVALID_TOKEN("invalid_token"),
    INVALID_OPERATION("invalid_operation"),
    INVALID_INPUT("invalid_input"),

    // File upload errors
    FILE_UPLOAD_FAILED("file_upload_failed"),
    FILE_SIZE_EXCEEDED("file_size_exceeded"),
    FILE_TYPE_NOT_ALLOWED("file_type_not_allowed"),
    FILE_NAME_INVALID("file_name_invalid"),
    FILE_CONTENT_INVALID("file_content_invalid"),
    FILE_VALIDATION_FAILED("file_validation_failed"),
    FILE_NOT_FOUND("file_not_found"),
    FILE_DELETION_FAILED("file_deletion_failed"),
    UPLOAD_RATE_LIMIT_EXCEEDED("upload_rate_limit_exceeded"),
    MALICIOUS_FILE_DETECTED("malicious_file_detected"),

    // Chat-specific errors
    CHAT_CHANNEL_NOT_FOUND("chat_channel_not_found"),
    CHAT_MESSAGE_NOT_FOUND("chat_message_not_found"),
    HUB_PARTICIPANT_NOT_FOUND("hub_participant_not_found"),

    // Brief-specific errors
    BRIEF_NOT_FOUND("brief_not_found"),
    BRIEF_ACCESS_DENIED("brief_access_denied"),
    BRIEF_TITLE_ALREADY_EXISTS("brief_title_already_exists");

    private final String code;

    ErrorCode(String code) {
        this.code = code;
    }

    /**
     * Returns the error code string used for frontend i18n translation.
     */
    public String getCode() {
        return code;
    }

    @Override
    public String toString() {
        return code;
    }
}
