package com.collabhub.be.exception;

import com.collabhub.be.exception.dto.ErrorDetail;
import com.collabhub.be.exception.dto.ErrorResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Global exception handler providing centralized exception handling.
 * Returns unified error response format for all exceptions.
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(BadRequestException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponse> handleBadRequestException(BadRequestException ex) {
        logger.warn("Bad request exception occurred: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(ex.getErrorCode().getCode(), ex.getMessage());
        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(UnauthorizedException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ResponseEntity<ErrorResponse> handleUnauthorizedException(UnauthorizedException ex) {
        logger.warn("Unauthorized exception occurred: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(ex.getErrorCode().getCode(), ex.getMessage());
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
    }

    @ExceptionHandler(ForbiddenException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ResponseEntity<ErrorResponse> handleForbiddenException(ForbiddenException ex) {
        logger.warn("Forbidden exception occurred: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(ex.getErrorCode().getCode(), ex.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorResponse);
    }

    @ExceptionHandler(NotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ErrorResponse> handleNotFoundException(NotFoundException ex) {
        logger.warn("Not found exception occurred: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(ex.getErrorCode().getCode(), ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    @ExceptionHandler(ConflictException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public ResponseEntity<ErrorResponse> handleConflictException(ConflictException ex) {
        logger.warn("Conflict exception occurred: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(ex.getErrorCode().getCode(), ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse);
    }

    @ExceptionHandler(InternalServerErrorException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResponse> handleInternalServerErrorException(InternalServerErrorException ex) {
        logger.error("Internal server error exception occurred: {}", ex.getMessage(), ex);

        ErrorResponse errorResponse = new ErrorResponse(ex.getErrorCode().getCode(), ex.getMessage());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponse> handleValidationException(MethodArgumentNotValidException ex) {
        logger.warn("Validation failed: {}", ex.getMessage());

        List<ErrorDetail> errorDetails = ex.getBindingResult()
            .getFieldErrors()
            .stream()
            .map(this::mapFieldErrorToErrorDetail)
            .collect(Collectors.toList());

        ErrorResponse errorResponse = new ErrorResponse(
            ErrorCode.VALIDATION_FAILED.getCode(),
            "Validation failed",
            errorDetails
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponse> handleConstraintViolationException(ConstraintViolationException ex) {
        logger.warn("Constraint violation: {}", ex.getMessage());

        List<ErrorDetail> errorDetails = ex.getConstraintViolations()
            .stream()
            .map(this::mapConstraintViolationToErrorDetail)
            .collect(Collectors.toList());

        ErrorResponse errorResponse = new ErrorResponse(
            ErrorCode.VALIDATION_FAILED.getCode(),
            "Validation failed",
            errorDetails
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResponse> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
        logger.warn("Invalid request format: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
            ErrorCode.INVALID_REQUEST_FORMAT.getCode(),
            "Invalid request format"
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ResponseEntity<ErrorResponse> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        logger.warn("Method not supported: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
            ErrorCode.METHOD_NOT_ALLOWED.getCode(),
            "HTTP method not supported"
        );

        return ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).body(errorResponse);
    }

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public ResponseEntity<ErrorResponse> handleMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException ex) {
        logger.warn("Media type not supported: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
            ErrorCode.UNSUPPORTED_MEDIA_TYPE.getCode(),
            "Media type not supported"
        );

        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body(errorResponse);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponse> handleMissingParameterException(MissingServletRequestParameterException ex) {
        logger.warn("Missing request parameter: {}", ex.getMessage());

        ErrorDetail errorDetail = new ErrorDetail(
            ex.getParameterName(),
            ErrorCode.FIELD_REQUIRED.getCode(),
            "This parameter is required"
        );

        ErrorResponse errorResponse = new ErrorResponse(
            ErrorCode.VALIDATION_FAILED.getCode(),
            "Missing required parameter",
            List.of(errorDetail)
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponse> handleTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        logger.warn("Type mismatch for parameter: {}", ex.getMessage());

        ErrorDetail errorDetail = new ErrorDetail(
            ex.getName(),
            ErrorCode.FIELD_INVALID_FORMAT.getCode(),
            "Invalid parameter format"
        );

        ErrorResponse errorResponse = new ErrorResponse(
            ErrorCode.VALIDATION_FAILED.getCode(),
            "Invalid parameter format",
            List.of(errorDetail)
        );

        return ResponseEntity.badRequest().body(errorResponse);
    }

    @ExceptionHandler(NoResourceFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ErrorResponse> handleNoResourceFoundException(NoResourceFoundException ex) {
        logger.warn("Resource not found: {}", ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
            ErrorCode.NOT_FOUND.getCode(),
            "Resource not found"
        );

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception ex) {
        logger.error("Unexpected error occurred", ex);

        ErrorResponse errorResponse = new ErrorResponse(
            ErrorCode.INTERNAL_SERVER_ERROR.getCode(),
            "An unexpected error occurred"
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    private ErrorDetail mapFieldErrorToErrorDetail(FieldError fieldError) {
        String fieldName = fieldError.getField();
        String defaultMessage = fieldError.getDefaultMessage();

        // Extract validation parameters from the field error
        Map<String, Object> params = extractValidationParameters(fieldError);

        // Determine error code based on validation annotation
        String errorCode = determineErrorCode(fieldError, params);

        return new ErrorDetail(fieldName, errorCode, defaultMessage, params);
    }

    private ErrorDetail mapConstraintViolationToErrorDetail(ConstraintViolation<?> violation) {
        String fieldName = violation.getPropertyPath().toString();
        String message = violation.getMessage();

        // Extract validation parameters from the constraint annotation
        Map<String, Object> params = extractConstraintParameters(violation);

        // Determine error code based on constraint annotation
        String errorCode = determineConstraintErrorCode(violation, params);

        return new ErrorDetail(fieldName, errorCode, message, params);
    }

    private Map<String, Object> extractValidationParameters(FieldError fieldError) {
        Map<String, Object> params = new HashMap<>();

        Object[] arguments = fieldError.getArguments();
        if (arguments != null && arguments.length > 1) {
            // Skip the first argument which is usually the field name
            for (int i = 1; i < arguments.length; i++) {
                Object arg = arguments[i];
                if (arg instanceof Number) {
                    // Handle numeric constraints like @Size, @Min, @Max
                    if (fieldError.getCode() != null) {
                        switch (fieldError.getCode()) {
                            case "Size":
                                if (i == 1) params.put("min", arg);
                                if (i == 2) params.put("max", arg);
                                break;
                            case "Min":
                                params.put("min", arg);
                                break;
                            case "Max":
                                params.put("max", arg);
                                break;
                            case "DecimalMin":
                                params.put("min", arg);
                                break;
                            case "DecimalMax":
                                params.put("max", arg);
                                break;
                        }
                    }
                }
            }
        }

        return params.isEmpty() ? null : params;
    }

    private Map<String, Object> extractConstraintParameters(ConstraintViolation<?> violation) {
        Map<String, Object> params = new HashMap<>();

        try {
            Annotation annotation = violation.getConstraintDescriptor().getAnnotation();
            Class<? extends Annotation> annotationType = annotation.annotationType();

            // Extract common validation parameters using reflection
            extractParameterIfExists(annotation, annotationType, "min", params);
            extractParameterIfExists(annotation, annotationType, "max", params);
            extractParameterIfExists(annotation, annotationType, "value", params);
            extractParameterIfExists(annotation, annotationType, "regexp", params);

        } catch (Exception e) {
            logger.debug("Could not extract constraint parameters", e);
        }

        return params.isEmpty() ? null : params;
    }

    private void extractParameterIfExists(Annotation annotation, Class<? extends Annotation> annotationType,
                                        String paramName, Map<String, Object> params) {
        try {
            Method method = annotationType.getDeclaredMethod(paramName);
            Object value = method.invoke(annotation);
            if (value != null) {
                params.put(paramName, value);
            }
        } catch (Exception e) {
            // Parameter doesn't exist or couldn't be accessed, ignore
        }
    }

    private String determineErrorCode(FieldError fieldError, Map<String, Object> params) {
        String code = fieldError.getCode();
        if (code == null) {
            return ErrorCode.FIELD_INVALID_FORMAT.getCode();
        }

        return switch (code) {
            case "NotNull", "NotEmpty", "NotBlank" -> ErrorCode.FIELD_REQUIRED.getCode();
            case "Size" -> {
                if (params != null && params.containsKey("min") && params.containsKey("max")) {
                    yield ErrorCode.FIELD_INVALID_LENGTH.getCode();
                } else if (params != null && params.containsKey("min")) {
                    yield ErrorCode.FIELD_TOO_SHORT.getCode();
                } else if (params != null && params.containsKey("max")) {
                    yield ErrorCode.FIELD_TOO_LONG.getCode();
                } else {
                    yield ErrorCode.FIELD_INVALID_SIZE.getCode();
                }
            }
            case "Min", "DecimalMin" -> ErrorCode.FIELD_INVALID_RANGE.getCode();
            case "Max", "DecimalMax" -> ErrorCode.FIELD_INVALID_RANGE.getCode();
            case "Pattern" -> ErrorCode.FIELD_INVALID_PATTERN.getCode();
            case "Email" -> ErrorCode.FIELD_INVALID_EMAIL.getCode();
            case "URL" -> ErrorCode.FIELD_INVALID_URL.getCode();
            case "Past", "Future", "PastOrPresent", "FutureOrPresent" -> ErrorCode.FIELD_INVALID_DATE.getCode();
            case "Digits" -> ErrorCode.FIELD_INVALID_NUMBER.getCode();
            default -> ErrorCode.FIELD_INVALID_FORMAT.getCode();
        };
    }

    private String determineConstraintErrorCode(ConstraintViolation<?> violation, Map<String, Object> params) {
        String annotationName = violation.getConstraintDescriptor().getAnnotation().annotationType().getSimpleName();

        return switch (annotationName) {
            case "NotNull", "NotEmpty", "NotBlank" -> ErrorCode.FIELD_REQUIRED.getCode();
            case "Size" -> {
                if (params != null && params.containsKey("min") && params.containsKey("max")) {
                    yield ErrorCode.FIELD_INVALID_LENGTH.getCode();
                } else if (params != null && params.containsKey("min")) {
                    yield ErrorCode.FIELD_TOO_SHORT.getCode();
                } else if (params != null && params.containsKey("max")) {
                    yield ErrorCode.FIELD_TOO_LONG.getCode();
                } else {
                    yield ErrorCode.FIELD_INVALID_SIZE.getCode();
                }
            }
            case "Min", "DecimalMin" -> ErrorCode.FIELD_INVALID_RANGE.getCode();
            case "Max", "DecimalMax" -> ErrorCode.FIELD_INVALID_RANGE.getCode();
            case "Pattern" -> ErrorCode.FIELD_INVALID_PATTERN.getCode();
            case "Email" -> ErrorCode.FIELD_INVALID_EMAIL.getCode();
            case "URL" -> ErrorCode.FIELD_INVALID_URL.getCode();
            case "Past", "Future", "PastOrPresent", "FutureOrPresent" -> ErrorCode.FIELD_INVALID_DATE.getCode();
            case "Digits" -> ErrorCode.FIELD_INVALID_NUMBER.getCode();
            default -> ErrorCode.FIELD_INVALID_FORMAT.getCode();
        };
    }
}
